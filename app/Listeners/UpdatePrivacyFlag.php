<?php

namespace App\Listeners;

use App\Events\PrivacySigned;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class UpdatePrivacyFlag
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\PrivacySigned  $event
     * @return void
     */
    public function handle(PrivacySigned $event)
    {
        foreach ($event->signers as $signer) {
            $signer->privacy = true;
            $signer->privacy_accepted_at = now();
            $signer->save();
        }
    }
}
