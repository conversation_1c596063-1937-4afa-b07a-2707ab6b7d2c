<?php

namespace App\Http\Controllers;

use App\Models\Pipeline;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class DashboardController extends Controller
{
    use CanSwitchTheme;

    public function getIndex(Request $request) 
    {
        $path = $this->getView(app('currentTenant')->name, "Dashboard");
        
        //$filters = $request->only(['search', 'searchByState', 'searchByTask']);

        // create 1000 random pipelines
        // for ($i = 0; $i < 1000; $i++) {
        //     Pipeline::factory()->create();
        // }


        // Pipeline data (currentTask, contractor, etc. loaded in toArray)
        $pipelines = Pipeline
            ::with('user')
            //->with('clients')
            //->filter($filters)
            ->orderBy('created_at', 'desc')
            ->get();
            //->paginate(5000)
            //->withQueryString();

        return Inertia::render($path, [
            'pipelines' => $pipelines,
            //'filters' => $filters,
        ]);    
    }
}
