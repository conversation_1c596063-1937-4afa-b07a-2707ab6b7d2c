<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class ProductsController extends Controller
{
    use CanSwitchTheme;

    public function getIndex()
    {
        $path = $this->getView(app('currentTenant')->name, "Products");

        return Inertia::render($path, [
            'products' => Product::with('company')->whereEnabled(1)->get(),
        ]);
    }

    public function getProduct(Product $product)
    {
        $path = $this->getView(app('currentTenant')->name, "ProductDetail");

        return Inertia::render($path, [
            'product' => Product::where('id', $product->id)->with(['company', 'coverages'])->first(),
        ]);
    }

}
