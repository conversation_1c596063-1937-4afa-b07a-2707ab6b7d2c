<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class SalesmenController extends Controller
{
    use CanSwitchTheme;

    public function getIndex()
    {
        $path = $this->getView(app('currentTenant')->name, "Salesmen");

        // create 100 random salesmen
        /*for ($i = 0; $i < 9; $i++) {
            $user = new User();

            // load network node POS1
            $node = \App\Models\NetworkNode::whereCode('POS1')->first();

            $user->forceFill([
                'password' => "asd",
                'node_id' => $node->id,
                'name' => fake()->name(), 
                'lastname' => fake()->lastName(), 
                'email' => fake()->unique()->safeEmail(),
            ])->save();

            $user->addRole('salesman');
        }*/

        $salesmen = User::whereHas(
            'roles', function($q){
            $q->where('name', 'salesman');
        })
        ->with('node')
        ->orderBy('active', 'desc')
        ->orderBy('lastname')
        ->get();

        return Inertia::render($path, [
            'salesmen' => $salesmen,
        ]);
    }

    public function postSalesman(Request $request, $userId)
    {
        $path = $this->getView(app('currentTenant')->name, "Salesmen");

        // Toggle stato del collaboratore
        User::find($userId)->update(['active' => ! $request->currentStatus]);

        $salesmen = User::whereHas(
            'roles', function($q){
            $q->where('name', 'salesman');
        })
        ->with('node')
        ->get();

        return Inertia::render($path, [
            'salesmen' => $salesmen,
        ]);
    }

    public function getSalesman($id)
    {
        $path = $this->getView(app('currentTenant')->name, "Salesman");

        $salesman = User::whereHas(
            'roles', function($q){
            $q->where('name', 'salesman');
        })
        ->with('pipelines')
        ->with('node')
        ->whereId($id)
        ->first();

        if (! $salesman) {
            abort(404);
        }

        return Inertia::render($path, [
            'network' => $salesman->node->get()->toTree(),
            'salesman' => $salesman,
        ]);
    }

}
