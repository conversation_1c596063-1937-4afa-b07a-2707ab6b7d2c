<?php

namespace App\Http\Controllers;

use App\Models\Pipeline;
use App\Models\Task;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Upnovation\Easyprofile\PipelineManager;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class PipelineController extends Controller
{
    use \Upnovation\Easyprofile\Traits\CanSwitchTheme;

    /**
     * Undocumented variable
     *
     * @var PipelineManager
     */
    protected $manager;

    public function __construct(PipelineManager $manager)
    {
        $this->manager = $manager;
    }

    public function getIndex(Pipeline $pipeline)
    {
        // make dynamic theme for tenant 
        $path = $this->getView(app('currentTenant')->name, "Pipeline");

        $pipeline = $pipeline
            ->load('tasks')
            ->load('user')
            ->load('user.node');

        $subjects = $pipeline->clients()
            ->with('person')
            ->with('enterprise')
            ->get();

        return Inertia::render($path, [
            'pipeline' => $pipeline,
            'subjects' => $subjects,
            'files' => $pipeline->getFiles(),
            'products' => $pipeline->getSelectedProducts(),
            'issuances' => $pipeline->getIssuances(),
        ]);
    }


    /**
     * Starts a new pipeline.
     *
     * @return void
     */
    public function postIndex()
    {
        $pipeline = $this->manager->start(auth()->user());

        return redirect()->route("pipeline.resume", ['pipeline' => $pipeline->id]);
    }

    /**
     * Resume a pipeline.
     *
     * @return void
     */
    public function getResume(Pipeline $pipeline)
    {
        if (! $task = $this->manager->resume($pipeline)) {
            return redirect()->route('dashboard');
        }

        return redirect()->action(
            [$task->controller, 'getIndex'],
            [$pipeline, $task]
        );
    }

    /**
     * Transition to next task.
     *
     * @return void
     */
    public function getNext(Pipeline $pipeline)
    {
        if (! $this->manager->next($pipeline)) {
            return redirect()->route('dashboard');
        }

        return redirect()->route("pipeline.resume", ['pipeline' => $pipeline->id]);
    }

    /**
     * Go to specific task.
     *
     * @param Pipeline $pipeline
     * @param Task $task
     * @return void
     */
    public function getGoto(Pipeline $pipeline, Task $task)
    {
        if (! $task = $this->manager->goto($pipeline, $task)) {
            throw new \Exception("Task is not accessible.");
        }

        return redirect()->action(
            [$task->controller, 'getIndex'],
            [$pipeline, $task]
        );
    }

    /**
     * Delete pipeline.
     *
     * @param Pipeline $pipeline
     * @return void
     */
    public function deleteIndex(Pipeline $pipeline)
    {
        $this->manager->delete($pipeline);

        return redirect()->route("dashboard", ['pipeline' => $pipeline->id]);
    }
}
