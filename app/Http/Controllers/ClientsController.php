<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Person;
use Inertia\Inertia;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class ClientsController extends Controller
{
    use CanSwitchTheme;

    public function getIndex()
    {
        $path = $this->getView(app('currentTenant')->name, "Clients");

        $clients = Client::with('person')->with('enterprise')->with('pipeline')->get();

        $clients = $clients->map(function ($client) {
            if ($client->enterprise_id && $client->enterprise) {
                $client->enterprise->rep = $client->enterprise->rep ?? new Person();
            }
            return $client;
        });

        return Inertia::render($path, [
            'clients' => $clients->map(function ($client) {
                return $client->person_id ? $client->person : null;
            })->filter()->unique('id')->values(),

            'enterprises' => $clients->map(function ($client) {
                return $client->enterprise_id ? $client->enterprise : null;
            })->filter()->unique('id')->values(),

        ]);    
    }
}

