<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Middleware;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class HandleInertiaRequests extends Middleware
{
    use CanSwitchTheme;

    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Defines the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function share(Request $request): array
    {
        if (! $user = auth()->user()) {
            return parent::share($request);
        }

        $currentTenant = app('currentTenant');

        $json = storage_path("header.{$currentTenant->name}.json");

        if (! file_exists($json)) {
            Log::debug("Loading default header json.");

            $json = storage_path("header.default.json");
        }

        if (! $headerJson = json_decode(file_get_contents($json))) {
            Log::error("Header JSON not found ($json).");
            
            abort(500);
        }

        $authorizedRoutes = [];

        // Passo all'utente solo le rotte che è autorizzato a vedere.
        foreach ($headerJson as $navItem) {
            if (array_intersect($navItem->authorized, $user->roles->pluck('name')->toArray())) {
                $authorizedRoutes[] = $navItem;
            }
        }

        return array_merge(parent::share($request), [
            'flash' => $this->getSessionFlash(),
            
            'user' => $user->load('roles'),
            'tech' => [
                'tenant' => app('currentTenant'),
                'EGG_TESTING_TOKEN' => env('EGG_TESTING_TOKEN'),
                'EGG_TESTING_USERID' => env('EGG_TESTING_USERID'),
            ],
            'navItems' => $authorizedRoutes ?: [],

            // @TODO/FIXME only load UI at some point (startup? login?)
            'ui' => $this->getUIConfig($currentTenant->name),
        ]);
    }

    protected function getSessionFlash()
    {
        try {
            $flashKeys = request()->session()->get('_flash.old', []);

            return collect($flashKeys)
                ->mapWithKeys(fn ($key) => [$key => request()->session()->get($key)])
                ->all();
        } catch (\Exception $e) {
            Log::error("Error retrieving session flash data: " . $e->getMessage());

            return [];
        }
    }
}
