<?php

namespace App\Models;

use App\Models\Scopes\RestrictedScope;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Pipeline extends Model
{
    use HasFactory, UsesTenantConnection;

    protected $fillable = ['user_id', 'client_id'];

    protected static function booted(): void
    {
        static::addGlobalScope(new RestrictedScope);
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where('egg_opportunity_id', 'like', '%'.$search.'%');
        });
        $query->when($filters['searchByState'] ?? null, function ($query, $search) {
            $query->where('state', 'like', '%'.$search.'%');
        });
    }

    public function user() : BelongsTo 
    {
        return $this->belongsTo(User::class);
    }

    public function tasks() : HasMany 
    {
        return $this->hasMany(Task::class);
    }

    public function getContractor() : Client | null
    {
        return $this->clients()
            ->where('role', 'contractor')
            ->first();
    }

    // All PERSON subjects involved in the pipeline
    public function getPersonSubjects($role) 
    {
        if (! $clients = $this->clients()->where('role', $role)->get()) {
            return [];
        }
        
        $result = [];

        foreach ($clients as $client) {
            if ($client->person_id) {
                $result[] = $client->person;
            } elseif ($client->enterprise_id) {
                $result[] = $client->enterprise->rep;
            }
        }

        return $result;
    }

    // All subjects involved in the pipeline
    public function getSubjects(string $role)
    {
        if (! $clients = $this->clients()->where('role', $role)->get()) {
            return [];
        }
        
        $result = [];

        foreach ($clients as $client) {
            if ($client->person_id) {
                $result[] = $client->person;
            } elseif ($client->enterprise_id) {
                $result[] = $client->enterprise;
            }
        }

        return $result;
    }

    public function clients() : HasMany 
    {
        return $this->hasMany(Client::class);
    }    

    public function profiles() : HasMany 
    {
        return $this->HasMany(Profile::class);
    }

    public function getProfile()
    {
        // Pipelines - Profiles is mapped as "HasMany", hence
        // each pipeline might have multiple profiles. Anyway this
        // is not the case, and we enforce an app-side constraint
        // so that pipelines can only have 1 profile.
        // Mapping the other way around, as a "HasOne", whould imply
        // a "profile_id" column in the pipelines table which would
        // invalidate the Tasks flexibility, where "profile" ,"survey",
        // etc. are only one of the possible implementations.

        return $this->profiles()->with('coverages')->first();
    }

    public function currentTask() : Task|null {
        // @FIXME! check possible regressions
        if ($this->state == 'closed') {
            return null;
        }

        return $this
            ->tasks()
            ->where('state', 'progress')
            ->orderBy('priority')
            ->first();
    }

    public function prioritizedTasks() : Collection {
        return $this->tasks()->orderBy('priority')->get();
    }

    public function form() : HasOne|null 
    {
        return $this->hasOne(Form::class);
    }

    public function getSelectedProducts()
    {
        if (! $this->getProfile()) {
            return null;
        }

        if (! $this->getProfile()->mapperResult()) {
            return null;
        }

        $results = $this->getProfile()->mapperResult()->where('confirmed', true)->get();

        return $results->map(function($result) {
            return $result->product;
        })->filter();
    }

    public function getFiles()
    {
        $taskIds = $this->tasks()->pluck('id');

        // Carica tutti i file collegati a questi task
        return File::with('document')->whereIn('task_id', $taskIds)->get();
    }

    public function getIssuances()
    {
        $taskIds = $this->tasks()->pluck('id');

        return Issuance::whereIn('task_id', $taskIds)->with('product')->get();
    }

    public function toArray()
    {
        $array = parent::toArray();

        $array['currentTask'] = $this->currentTask();

        $array['client'] = null;

        if ($array['client'] = $this->getContractor()) {
            $array['client']->load('person')->load('enterprise');
        }

        //$array['client'] = $this->getContractor()->load('person')->load('enterprise');

        return $array;  
    }
}
