<?php namespace App\Models\Rules;

use Illuminate\Contracts\Validation\Rule as RuleContract;
use Illuminate\Database\Eloquent\Model;

/**
 * This rule is required because the multitenancy package does not support the unique rule with a tenant connection.
 */
class ExistsInTenant implements RuleContract
{
    protected Model $model;
    protected string $column;

    public function __construct(Model $model, string $column)
    {
        $this->model = $model;
        $this->column = $column;
    }

    public function passes($attribute, $value)
    {
        return $this->model->where($this->column, $value)->exists();
    }

    public function message()
    {
        return __('validation.exists_in_tenant', ['attribute' => $this->column, 'model' => $this->model->getDisplayName()]);
    }
}