<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Profile extends Model
{
    use HasFactory, UsesTenantConnection;

    public function client() : BelongsTo 
    {
        return $this->belongsTo(Client::class);
    }

    public function coverages() : BelongsToMany
    {
        return $this->belongsToMany(Coverage::class);
    }

    public function pipeline() : BelongsTo
    {
        return $this->belongsTo(Pipeline::class);
    }

    public function mainCoverages() : Collection
    {
        return $this->coverages()->where('type', 'main')->get();
    }

    public function mapperResult()
    {
        return $this->hasMany(MapperResult::class)->with('product');
    }
}
