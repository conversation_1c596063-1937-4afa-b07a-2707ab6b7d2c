<?php

namespace App\Models;

use App\Models\Scopes\DocumentsScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Document extends Model
{
    use HasFactory, UsesTenantConnection;

    protected $casts = [
        'signers' => 'array',
        'signatures' => 'array',
        'overlays' => 'array',
    ];

    public $overlayArray = [];

    protected static function boot()
    {
        parent::boot();

        /*
        static::saving(function ($document) {
            $array = [];

            foreach($document->overlayArray as $key => $overlay) {
                $array[$key] = [
                    'class' => get_class($overlay),
                    'page' => $overlay->page,
                    'x' => $overlay->x,
                    'y' => $overlay->y,
                    'settings' => $overlay->settings ?? [],
                    'value' => $overlay->value ?? null,
                ];
            }

            $document->overlays = $array;
        });*/
    }

    protected static function booted(): void
    {
        static::addGlobalScope(new DocumentsScope);
    }

    public function node() : BelongsTo
    {
        return $this->belongsTo(NetworkNode::class, 'node_id');
    }

    public function template() : HasOne
    {
        // HasMany in teoria, ma in pratica ha un unico template;
        // HasMany ha senso per le istanze.
        return $this->HasOne(File::class)->whereType('template');
    }


    public function products() : BelongsToMany
    {
        // @TODO for now, even if this is a many-to-many relationship,
        // document should have 1 single product; hence the getProduct() method.
        return $this->belongsToMany(Product::class);
    }

    public function getProduct()
    {
        return $this->products()->first();
    }

    public function loadConfiguration()
    {
        return config("documents.{$this->template->configName}");
    }

    public function getOverlayArray()
    {
        if ($this->overlayArray) {
            return $this->overlayArray;
        }

        // @FIXME
        // Quick fix for nested overlays (like RadioOverlay); the actual system is not
        // capale of instantiating nested overlays correctly.
        // Fix: read them from configuration file for now.
        if (! $this->template) {
            throw new \Exception("Document template is not set for Document ID {$this->id}");
        }

        return $this->overlayArray = $this->loadConfiguration()['document']['overlayArray'];
        
        
        
        // ------------------------------------------------

        foreach($this->overlays as $overlay) {
            $class = $overlay['class'];

            if (! class_exists($class)) {
                throw new \Exception("Overlay class '$class' does not exist.");
            }

            $this->overlayArray[] = new $class(
                $overlay['page'],
                $overlay['x'],
                $overlay['y'],
                $overlay['settings'] ?? [],
                $overlay['value'] ?? null
            );
        }

        return $this->overlayArray;
    }
}
