<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;
use Illuminate\Support\Str;

class File extends Model
{
    use HasFactory, UsesTenantConnection;

    protected $fillable = ['disk', 'path', 'filename', 'task_id', 'document_id', 'type', 'displayName'];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($file) {
            if (! $file->uuid) {
                $file->uuid = Str::uuid()->toString();
            }
        });
        
    }

    public function document() : BelongsTo
    {
        return $this->belongsTo(Document::class, 'document_id');
    }

    public function getFullPath()
    {
        if (! $tenant = app('currentTenant')->name) {
            throw new \Exception("No tenant set for file path resolution.");
        }

        $filepath = "{$tenant}/" . ($this->path ? $this->path . "/" : ""). "{$this->filename}";

        /** @var \Illuminate\Filesystem\FilesystemAdapter $disk */
        $disk = Storage::disk($this->disk);
        
        return $disk->path($filepath);
    }

}
