<?php

namespace App\Models;

use App\Models\Interfaces\Clientable;
use App\Models\Scopes\PeopleScope;
use App\Models\Traits\CanDisplayName;
use App\Models\Traits\CanGloballySearch;
use App\Models\Traits\CanSafelyFill;
use App\Models\Traits\HasAddress;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Person extends Model implements Clientable
{
    use HasFactory, UsesTenantConnection, CanSafelyFill, HasAddress, CanDisplayName, CanGloballySearch;

    protected $fillable = [
        'name',
        'lastname',
        'birthdate',
        'birthplace',
        'email',
        'taxCode',
        'phone',
    ];

    protected $casts = [
        'birthdate' => 'date',
        'documents' => 'array',
    ];

    protected static function booted(): void
    {
        static::addGlobalScope(new PeopleScope);

        static::saving(function ($person) {
            if (isset($person->taxCode)) {
                $person->taxCode = strtoupper($person->taxCode);
            }
        });
    }

    public function addresses() : HasMany
    {
        return $this->hasMany(Address::class);
    }

    public function pipelines()
    {
        return $this->hasManyThrough(
            Pipeline::class, // Il modello finale
            Client::class,   // Il modello intermedio
            'person_id',     // Foreign key su Client che punta a Person
            'id',            // Foreign key su Pipeline che punta a Pipeline (di solito 'id')
            'id',            // Local key su Person
            'pipeline_id'    // Local key su Client che punta a Pipeline
        );
    }

    public function enterprises()
    {
        return $this->hasMany(Enterprise::class, 'rep_id');
    }

    public function getDocument(string $type)
    {
        if($this->documents[$type]) {
            $document = $this->documents[$type];
            $document['expiry'] = Carbon::parse($this->documents[$type]['expiry'] ?? null);
            $document['issuerDate'] = Carbon::parse($this->documents[$type]['issuerDate'] ?? null);
            return (object)$document;
        }

        return null;
    }

    public function getType() : string
    {
        return 'individual';
    }

    public function __toString()
    {
        return "{$this->name} {$this->lastname} {$this->taxcode}";
    }
    

    //
    // @TODO?
    // SignerInterface methods
    //
    //
    public function getName(): string
    {
        return $this->name;
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    // @todo refactor to HasAddress trait + Addressable interface 
    public function getAddress(string $type): ?Address
    {
        return $this->addresses()->where('type', $type)->first();   
    }

    // @todo subject interface (for pdf compiling)
    public function getSubjectAddress(): ?Address
    {
        return $this->addresses()->where('type', 'residence')->first();   
    }

    public function getPrintableBirthdate()
    {
        if (! $this->birthdate) {
            return null;
        }

        return $this->birthdate->format('d/m/Y');
    }
}
