<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, UsesTenantConnection;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function addRole(string $name) : Role
    {
        if (! $role = Role::whereName($name)->first()) {
            throw new Exception("Role {$name} doesn't exist.");
        }

        $this->roles()->save($role);

        return $role;
    }

    public function roles() : BelongsToMany 
    {
        return $this->belongsToMany(Role::class);
    }

    public function hasRole(string $role) : bool
    {
        return $this->load('roles')->roles->contains('name', $role);
    }

    public function rolesToString() : string
    {
        return $this->roles->implode('name', ',');
    }

    public function pipelines() : HasMany
    {
        return $this->HasMany(Pipeline::class);
    }

    public function node() : BelongsTo
    {
        return $this->belongsTo(NetworkNode::class);
    }
}
