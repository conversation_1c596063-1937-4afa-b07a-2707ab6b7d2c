<?php namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class Develop extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:init {--tenant=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Develop database init.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        
        $tenants = [
            'default' => [
                'name' => 'default',
                'domain' => 'default.laravel.test',
            ],
            'dorotea' => [
                'name' => 'dorotea',
                'domain' => 'dorotea.laravel.test',
            ],
        ];
        
        // Don't refresh landlord if we are working with a single tenant.
        if (is_null($this->option('tenant'))) {
            Artisan::call('ep:install', [
                "--key" => env('APP_KEY'),
            ], $this->output);
        }

        foreach ($tenants as $tenant) {
            if ($this->option('tenant') && $this->option('tenant') != $tenant['name']) {
                continue;
            }

            Artisan::call('ep:tenant', [
                "--name" => $tenant['name'],
                "--domain" => $tenant['domain'],
            ], $this->output);
        }

        if (env('APP_ENV') == 'testing') {
            Artisan::call('ep:tenant', [
                "--name" => "tenant1",
                "--domain" => "localhost",
            ], $this->output);

            Artisan::call('ep:tenant', [
                "--name" => "tenant2",
                "--domain" => "tenant2.laravel.test",
            ], $this->output);
        }
        
        return Command::SUCCESS;
    }
}
