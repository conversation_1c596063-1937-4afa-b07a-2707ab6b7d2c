<?php

namespace App\Console\Commands;

use Database\Seeders\Tenants\DoroteaSeeder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Models\Tenant;

class InstallTenant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ep:tenant {--name=} {--domain=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Install a tenant.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $name = $this->option('name');

        if (! in_array(env('APP_ENV'), ['testing', 'local']) && ! $this->confirm("Questa operazione installerà il tenant {$name} da zero, azzerando il suo database. Sicuro?")) {
            return Command::SUCCESS;
        }

        if (! $tenant = $this->getTenant()) {
            $prefix = env('APP_ENV') == 'testing' ? 'testing' : 'ep';

            $schema = "{$prefix}_{$name}";

            DB::connection('landlord')->table('tenants')->insert(['name' => $name, 'domain' => $this->option('domain'), 'database' => $schema]);

            $tenant = $this->getTenant();
        }

        $tenantName = ucfirst($tenant->name);

        //Run common migrations
        $this->info("Running common migrations.");
        Artisan::call('tenants:artisan "migrate:fresh --database=tenant --path=database/migrations/common"' . ' --tenant=' . ($tenant->id), [], $this->output);

        //Refresh database
        $this->info("Running standard tenant migrations.");
        Artisan::call('tenants:artisan "migrate --database=tenant --path=database/migrations/tenant"' . ' --tenant=' . ($tenant->id), [], $this->output);

        //Run tenant specific migrations
        $this->info("Running specific tenant migrations.");
        Artisan::call('tenants:artisan "migrate --database=tenant --path=database/migrations/' . ($tenant->name) . '" --tenant=' . ($tenant->id), [], $this->output);

        $networkSeeder = "{$tenantName}NetworkSeeder";

        if (file_exists(database_path("seeders/{$networkSeeder}.php")) && class_exists("Database\\Seeders\\{$networkSeeder}")) {
            $this->info("Running tenant network seeder");

            Artisan::call('tenants:artisan "db:seed --database=tenant --class='.$networkSeeder.'"' . ' --tenant=' . ($tenant->id), [], $this->output);
        }

        // Seed catalog.
        $this->info("Running catalog seeder.");
        Artisan::call('tenants:artisan "db:seed --database=tenant --class=CatalogSeeder"' . ' --tenant=' . ($tenant->id), [], $this->output);

        // Seed Roles.
        $this->info("Running EasyProfile seeder.");
        Artisan::call('tenants:artisan "db:seed --database=tenant --class=EasyprofileSeeder"' . ' --tenant=' . ($tenant->id), [], $this->output);

        // Geo seed.
        $this->info("Running Geo seeder.");
        Artisan::call('tenants:artisan "geo:import --limit=50"' . ' --tenant=' . ($tenant->id), [], $this->output);

        // Custom seed.
        $customSeeder = "{$tenantName}CustomSeeder";

        if (file_exists(database_path("seeders/{$customSeeder}.php")) && class_exists("Database\\Seeders\\{$customSeeder}")) {
            $this->info("Running tenant custom seeder");

            Artisan::call('tenants:artisan "db:seed --database=tenant --class='.$customSeeder.'"' . ' --tenant=' . ($tenant->id), [], $this->output);
        }

        if (env('APP_ENV') != 'production' && $this->confirm("Vuoi creare utenze di test per il tenant {$tenant->name}?")) {
            Artisan::call('tenants:artisan "db:seed --database=tenant --class=UserSeeder"' . ' --tenant=' . ($tenant->id), [], $this->output);
        }

        return Command::SUCCESS;
    }

    protected function getTenant()
    {
        $name = $this->option('name');

        return Tenant::whereName($name)->first();
    }
}
