# Quickstart


## Primo setup

L'ambiente di sviluppo (windows) è composto da tre elementi:
- ambiente *host* 
- ambiente *guest*
- ambiente *docker*

L'ambiente host (sistema windows) deve eseguire le WSL per poter
gestire correttamente docker e *sail*. Per l'installazione bisogna
seguire gli step indicati nella documentazione di Laravel, che
spiegano come installare Docker Desktop e le WSL.

https://laravel.com/docs/11.x/installation#sail-on-windows
https://docs.docker.com/desktop/wsl/

Potrebbe accadere, a seconda delle configurazioni, che le versioni
delle varie cli (php e node) non siano coerenti con le dichiarazioni
nel *composer.json* e quindi la cli nel sistema host potrebbe non 
funzionare. In questi casi i comandi vanno eseguiti nel terminale
*guest* (debian, ubuntu, etc., quello installato tramite wsl) oppure
direttamente nel container docker.

Dopodiché:

> composer update

> npm update

Prima di eseguire il comando up è necessario disporre di un file .env,
in quanto la configurazione viene utilizzata da docker-compose. Questi
sono i parametri necessari per consentire la build dei container

 ```
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=sail
DB_PASSWORD=password
 ```

 Una volta inizializzato l'ambiente queste variabili non sono più utilizzate
 dall'applicazione. Copiare questi valori nell'env di riferimento, oppure se
 rinominare .env.example in .env

> vendor/bin/sail up

In caso di errore "Docker is not running" su windows:
- controllare se Docker Desktop è andato in modalità risparmio energetico
- potrebbe essere necessario esegire sail up con sudo

Per accedere al terminale guest (wsl) raccomando di usare Windows Terminal.
Per accedere al terminale del container docker: 

> vendor/bin/sail shell

Tutti i comandi cli incidono sulla stessa directory in quanto questa è 
monatata contemporaneamente su tutti e tre i sistemi (host, guest, docker)
per cui è ininfluente da dove questi vengono eseguiti - salvo che potrebbe
capitare che l'host abbia una diversa versione di php, il che potrebbe
causare problemi negli update composer, etc. In questo caso può convenire 
eseguire le cli direttamente in docker.

## Hosts setup

```
# /etc/hosts o C:\Windows\System32\drivers\etc\hosts

127.0.0.1 laravel.test tenant1.laravel.test tenant2.laravel.test default.laravel.test dorotea.laravel.test
```

## Database
Setup database in .env
```
DB_CONNECTION=pleasedontshowthewarning
DB_HOST=pleasedontshowthewarning
DB_PORT=pleasedontshowthewarning
DB_DATABASE=pleasedontshowthewarning
DB_USERNAME=pleasedontshowthewarning
DB_PASSWORD=pleasedontshowthewarning

LANDLORD_DB_CONNECTION=landlord
LANDLORD_DB_HOST=mysql
LANDLORD_DB_PORT=3306
LANDLORD_DB_DATABASE=laravel
LANDLORD_DB_USERNAME=sail
LANDLORD_DB_PASSWORD=password

TENANT_DB_CONNECTION=tenant
TENANT_DB_HOST=mysql
TENANT_DB_PORT=3306
TENANT_DB_DATABASE=null
TENANT_DB_USERNAME=ep_tenant
TENANT_DB_PASSWORD=password
```

Il primo blocco di parametri serve per evitare che phpunit generi un warning quando esegue i test.

Start docker (terminale guest WSL).

> vendor/bin/sail up -d

Setup landlord db for sail.

>  vendor/bin/sail artisan migrate --path=database/migrations/landlord --database=landlord

Accedere a phpmyadmin: localhost:8080, laravel.test:8080 (sail lo configura in automatico sulla 8080).

L'applicazione ha bisogno dei seguenti database:
- landlord (il master del sistema multitenant), nell'ambiente di sviluppo questo db si chiama *laravel*
- un database per ogni tenant, solitamente ep_NOMETENANT

Vanno quindi settati in mysql gli utenti con i permessi necessari per agire su questi database.
La libreria usata per la gestione multitenant (spatie) attualmente prevede un utente per accesso
al database landlord, e un unico utente per accesso a tutti i database dei tenant. E' una limitazione
che prima o poi bisognerà superare, ma al momento è cosi.

Setup mysql users.

```sql
CREATE USER 'ep_tenant'@'%' IDENTIFIED BY 'password';
GRANT USAGE ON *.* TO 'ep_tenant'@'%';
ALTER USER 'ep_tenant'@'%' REQUIRE NONE WITH MAX_QUERIES_PER_HOUR 0 MAX_CONNECTIONS_PER_HOUR 0 MAX_UPDATES_PER_HOUR 0 MAX_USER_CONNECTIONS 0;
GRANT ALL PRIVILEGES ON `ep_default`.* TO 'ep_tenant'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON `ep_dorotea`.* TO 'ep_tenant'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON `testing_default`.* TO 'ep_tenant'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON `testing_tenant1`.* TO 'ep_tenant'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON `testing_tenant2`.* TO 'ep_tenant'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON `testing_dorotea`.* TO 'ep_tenant'@'%' WITH GRANT OPTION;
```

Create tenant database.
```sql
CREATE DATABASE IF NOT EXISTS ep_default CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
CREATE DATABASE IF NOT EXISTS ep_dorotea CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
```

Create testing database.
```sql
CREATE DATABASE IF NOT EXISTS testing_landlord CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
CREATE DATABASE IF NOT EXISTS testing_default CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
CREATE DATABASE IF NOT EXISTS testing_tenant1 CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
CREATE DATABASE IF NOT EXISTS testing_tenant2 CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
CREATE DATABASE IF NOT EXISTS testing_dorotea CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
```

Una volta settati gli utenti, i permessi e creati i database, abbiamo un Command che si occupa
di creare l'ambiente di sviluppo. Per visualizzare i comandi artisan raw, vedere dentro i vari
Command. Per creare l'ambiente:

> vendor/bin/sail artisan dev:init

Questo comando può essere reiterato infinite volte. Equivale ad un totale reset del database con
installazione dei seeder necessari.

Si può usare un parametro per specificare il tenant:

> dev:init --name=nometenant

Questo agisce solo sul tenant e non altera il database landlord. 

**NOTE PER TENANT DOROTEA**

**Per il tenant dorotea, quando viene richiesto se creare gli utenti di test, scegliere NO** in quanto questi 
saranno automaticamente creati dal suo seeder. E' invece raccomandato creare quelli del tenant default.

**Eseguire anche l'upgrade per dorotea**
Per allineare il database, dopo dev:init, bisogna anche eseguire il comando di aggiornamento perché
a seguito dei vari sviluppi dorotea non è più compatibile con le migration di default.

> ep:update-tenant --name=dorotea

**Chiaramente questo è da intendersi e va fatto solo su ambienti di sviluppo/staging, l'ambiente di produzione va gestito in altro modo**

### Migrations
Struttura della directory migrations:
- _dev contiene dei placeholder o semi-lavorati. Non è rilevante.
- common contiene tutte le migration che devono essere eseguite sia in landlord, che nei database dei tenant
- tenant contiene solo le migration da eseguire nei database dei tenant
- possono esserci altre directory, il cui nome deve coincidere con quello di un tenant, che conterranno migration da eseguire solo sullo specifico tenant
-- la migration specifica del tenant può a sua volta contenere una ulteriore directory di `maintenance`: questa deve contenere le migration da eseguire post-produzione, ovvero su un sistema già in esercizio. Non sono eseguite da `dev:init`.

In generale non bisogna eseguire migration di tipo raw, invece bisogna usare l'apposito Command:
> vendor/bin/sail artisan ep:tenant-update {--name=}

Questo esegue tutte le migration necessarie. L'opzione *name* attualmente non è implementata.

C'è anche un'altra opzione che può essere comoda in fase di sviluppo, ovvero il command `dev:seed`.
Le opzioni obbligatorie sono il nome del tenant e il nome del seeder.

> dev:seed --tenant=nometenant --seeder=FooSeeder

Si può specificare anche di eseguire contestualmente una migration o una rollback della singola
migration sullo specifico tenant. Chiaramente, in caso di rollback non viene eseguito il seeder.

Esegue migration e seeder
> dev:seed --tenant=nometenant --seeder=FooSeeder --migrate

Esegue rollback
> dev:seed --tenant=nometenant --rollback



## Develop
L'ambiente di sviluppo è basato su Sail e Vite. Per entrare in modalità sviluppo:
> npm run dev

> http://default.laravel.test

# Build&Deploy 

## Prepare server
Il database del server va prima preparato. Questa operazione va fatta solo in fase di setup iniziale del server,
difficile che sia necessario ripeterla, comunque è analoga al setup dell'ambiente di sviluppo:


Mysql users.

```sql
-- landlord
CREATE DATABASE easyprofile CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
-- tenants
CREATE DATABASE ep_default CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
CREATE DATABASE ep_dorotea CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
-- users: RICONTROLLARE
CREATE USER 'easyprofile'@'localhost' IDENTIFIED VIA mysql_native_password USING PASSWORD('_set_password_here');
CREATE USER 'ep_tenant'@'localhost' IDENTIFIED VIA mysql_native_password USING PASSWORD('_set_password_here');
GRANT USAGE ON *.* TO 'easyprofile'@'localhost' REQUIRE NONE WITH MAX_QUERIES_PER_HOUR 0 MAX_CONNECTIONS_PER_HOUR 0 MAX_UPDATES_PER_HOUR 0 MAX_USER_CONNECTIONS 0;
GRANT USAGE ON *.* TO 'ep_tenant'@'localhost' REQUIRE NONE WITH MAX_QUERIES_PER_HOUR 0 MAX_CONNECTIONS_PER_HOUR 0 MAX_UPDATES_PER_HOUR 0 MAX_USER_CONNECTIONS 0;
GRANT ALL PRIVILEGES ON `easyprofile`.* TO 'easyprofile'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON `ep_default`.* TO 'ep_tenant'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON `ep_dorotea`.* TO 'ep_tenant'@'localhost' WITH GRANT OPTION;
```

Setup landlord (key è la KEY presente nel file env):

> vendor/bin/sail artisan ep:install --key=key

> vendor/bin/sail artisan ep:tenant --name=nometenant --domain=nometenant.easyprofile.it

Nota: attualmente il vhost è configurato nell'ambiente di produzione **senza** il wildcard,
quindi per ogni nuovo tenant va aggiunto il relativo alias.

## Accesso
Per autologin in ambiente di sviluppo (local/dev) usare le rotte /debug e /debug/admin.
Ricorda che per il tenant Dorotea non viene usato lo UserSeeder default.

## Deploy
La prima volta bisogna generare la chiave rsa nell'ambiente guest, e poi copiarla nel server.

> ssh-keygen -b 4096 -t rsa 

### Raw build
La build può essere creata con comandi raw.

I comandi passano delle variabil di ambiente al frontend Vue
e questi sono usati per applicare degli switch che mostrano o 
nascondono alcuni elementi.

Build per l'ambiente di sviluppo
> npm run build

Build per l'ambiente di produzione
> npm run build-production

Build per l'ambiente staging
> npm run build-staging

### Envoy

>  php vendor/bin/envoy run deploy --target=env_name

Il comando chiede un prompt per ogni passaggio, ma in sostanza:
- crea la build degli asset frontend
- esegue i test
- esegue un rsync dry
- esegue rsync
- esegue il Command di update

**Nota: quando si esegue il deploy il comando npm run dev deve essere interrotto, altrimenti effettua l'upload dei file hot di vite**

Nota: se capita l'errore "Envoy.blade.php not found." error, riavvia il terminale WSL2.

Ci sono nella codebase due liste di inclusioni/esclusioni (rsync-excludes-*) che vengono usate
da rsync per capire su quali directory agire in caso di prima installazione o di update.
In genere si usa solo l'update.

## Catalogo prodotti
- Catalog.php: definisce gli item che devono essere creati in prima configurazione
- CatalogSeeder.php: usa le definizioni di Catalog per creare tutti gli item

In caso di aggiornamento post-produzione sui tenant, aggiunte al catalogo dovranno essere configurate:
- in CatalogSeeder, per consentire lo sviluppo con dev:init
- in una specifica migration, che deve usare gli stessi elementi definiti in Catalog

## Documenti
Un po' di nozioni sui documenti e la loro gestione. Prima di tutto la struttura del database, che è organizzata
su due livelli.
- document: definizione meta data
- files: definizione file fisico, ad ogni record deve corrispondere un elemento del filesystem

I file vanno manipolati sempre attraverso il FileManager per garantire consistenza tra i tenant; 
l'unica eccezione è il PDFProcessor, in quanto fpdf vuole creare lui i file fisici (o comunque non
ho ancora esplorato come modificare questo comportamento): per questo il processor accetta in input
una istanza di File che definisce la posizione filesystem.

Attualmente i documenti possono essere delle seguenti tipologie.
### Precontrattuali: 
I vari privacy, mup, demand and needs, etc., che sono definiti in config/easyprofile.php.
Questi vengono installati nel seeder EasyprofileSeeder (che va aggiornato perché non è tenant-aware).
"Installare" un file significa (attraverso DocumentInstaller)
- leggere la configurazione, che sta dentro config/documents
- verificare che il file fisico esista nel path configurato
- creare il record nella tabella documents
- creare il record nella tabella files e collegare i due

### Form di prodotto
- tipologia: product-form
- rappresenta una scheda adesione o scheda raccolta dati
- queste schede sono compilate a partire da un form, che usa i valori inseriti per iniettarli nel PDFProcessor

### File di polizza
Un po' una forzatura del sistema. Poiché:
- durante la fase di emissione è necessario che sia caricato un filecontenente la polizza (generata altrove, su altri sistemi)
- non è accettato che ci sia un file nel filesystem ma non salvato nella tabella files
- questi file necessitano di metadati (salvati in documents)
per questi motivi viene creato un record di documento "fittizio" che sarà poi collegato alla istanza di file.

- tipologia: product-policy
- rappresenta il file che sarà poi caricato dal backoffice

## Files
Una nota sulle tipologie (files.type).
- template: tipicamente un template "vuoto" su cui il PDFProcessor stamperà i dati (esempio: privacy)
- compiled: file transitori, pdf compilati in attesa di essere lavorati da qualche processo
- signable: file compilati e pronti per la firma
- folder: archivio zip di file firmati 

**Importante**
In particolare su *signable*: i task di tipo 'signature', al caricamento, prendono tutti i file signable
della pipeline (qualsiasi task della pipeline) e li includono nel folder di firma.

## Configurazione delle firme
- tutti i task possono avere nella loro configurazione un campo 'compile' che indica quali file devono essere preparati. Questo avviene in task.finalize e può includere solo file di tipo statico e predeterminato (es. privacy, incarico, etc.)
- @todo verificare come funziona 'config' nei task di tipo signature che non me ricordo piu

### Firma elettronica
Codici evento inviati da IGSign al callback:
- 1 → FOLDER_CREATE - Fired whenever a folder is created.
- 2 → FOLDER_UPDATE - Fired whenever a folder is updated.
- 3 → STEP_CREATE - Fired when a workflow step is created
- 4 → STEP_COMPLETED - Fired when a step is completed
- 5 → FILE_DOWNLOADED - Fired when a document is downloaded
- 6 → QUICK_SIGN - Fired on quicksign signature
- 7 → DOCUMENT_CREATE - Fired when a folder document is created
- 8 → ACTION_CREATE - Fired whenever a signature or approval task is created
- 9 → ACTION_DELETE - Fired whenever a signature or approval task is removed
- 10 → ACTION_SIGN - Fired whenever a signature, approval or refusal occurs
- 11 → FOLDER_COMPLETE - Fired when all signature / approval actions inside a folder have been successfully completed
- 12 → FOLDER_START - Fired when a folder starts
- 13 → FOLDER_ABORT - Fired when a folder is aborted
- 14 → FOLDER_EXPORT - Fired whenever a folder was exported
- 15 → REPORT_READY - Fired whenever a folder is completed, and the Report is ready
- 16 → DOCUMENT_FILLED - Fired whenever a document is filled
- 17 → ACTION_ERROR - Fired when an action is not completed due to error during


## Emissione
TODO


# Note su Configurazione form
Job field:
- form will show manually-entered data
- job value in the form must match the configuration specified in config/easyprofile.php
- profiles table will store the job value
- such value is used in the requiredJobs|excludedJobs fields in coverage_product table

Length field:
- it's the product length, in years
- can be null
- form will show manually-entered data
- length values in the form must match the configuration specified in config/easyprofile.php
- profiles table will store the job value
- each product may have a minLen, maxLen couple
- StageConstraints will load the length from client's profile, then get the values from config/easyprofile.php in order to perform constraints check

# Note su clienti e dati anagrafici
TODO: strategia di aggiornamento retrocompatibile per Dorotea.

In prima iterazione le scelte sono state le seguenti:

* !!BREAKING CHANGE!! pipelines.client_id è stato rimosso; la relazione adesso è invertita e definita in clients.pipeline_id
* la tabella clients fondamentalmente è diventata la pivot che mette in relazione
    * la pipeline
    * il soggetto interessato, con indicazione del ruolo (contraente, assicurato, etc.)
    * il soggetto interessato può essere una persona fisica o giuridica. Questa cosa si desume dalle Fk person_id e enterprise_id: una sola delle due può essere valorizzata. Questo vincolo non può essere applicato a livello DB perciò dovrà essere esercitato a livello applicativo
    * name è solo un nome mnemonico ricavato dall'anagrafica del soggetto
* ogni persona definita in people può avere più indirizzi
    * ogni indirizzo può essere qualificato (indirizzo di residenza, di nascita, sede legale, etc)
    * per il geocoding attualmente si è importato un sql statico: https://www.gardainformatica.it/database-comuni-italiani#scarica
    * è stata caricata una sola tabella dove la PK è il cap, che si porta dietro tutte le informazioni necessarie, e funge da FK nella tabella degli indirizzi
* !!BREAKING CHANGE!! profiles.client_id è stato rimosso; la fk era ridondante e incompatibile con questo aggiornamento  

In questo cambiamento ho aggiornato le migration in questo modo:
* Inizialmente la migration delle pipelines aggiungeva il campo client_id
* Ho aggiornato la migration delle pipelines per non aggiungerlo più
* Nelle migration di dorotea (l'unico tenant in esecuzione ad oggi) ho aggiunto la gestione di questo campo,
creandolo e poi ridroppandolo nella procedura di upgrade, solo per avere un ambiente di sviluppo consistente.
* La migration di upgrade successivamente ricancella questo campo anche dalla struttura dorotea

# Testing
Per eseguire le migration in ambiente di test bisogna eseguire dev:init ma
forzando il comando ad agire con i valori impostati in .env.testing. Non
ho trovati altri modi oltre quello evidenziato sotto.

```
vendor/bin/sail shell
APP_ENV=testing php artisan dev:init
```

Adesso che il DB è allineato si possono eseguire i test direttamente dal terminale WSL, ma sempre
entrando nella shell della macchina per forzare la configurazione di .env.testing

```
vendor/bin/sail shell
APP_ENV=testing php artisan test
```
