<?php

use App\Events\PrivacySigned;
use App\Models\Enterprise;
use App\Models\Person;
use App\Models\Rules\ExistsInTenant;
use App\Models\Rules\UniqueInTenant;
use App\Models\User;
use App\Models\Zip;
use Illuminate\Validation\Rule;
use Upnovation\Easyprofile\Signature\IGSign\IGSign;
use Upnovation\Easyprofile\Signature\IGSignClient;
use Upnovation\Easyprofile\Tasks\Signature\SignatureManager;

return [
    'mapperLog' => true,
    
    'templateOverrides' => [
        'dorotea' => [
            'Dashboard' => 'Dorotea/Dashboard',
            'Clients' => 'Dorotea/Clients',
            'Salesmen' => 'Dorotea/Salesmen'
        ],
    ],

    // @TODO
    'authOverrides' => [
        'dorotea' => '',
    ],

    'clients' => [
        'dorotea' => [
            //'model' => App\Models\Dorotea\Client::class,
        ],
    ],

    'modules' => [
        'documents' => [
            'enabledTenants' => 'default',
            'docTypes' => ['all-3', 'all-4', 'mup', 'privacy', 'demands-and-needs', 'consent-digital-sending', 'receipt-statement', 'assignment', 'product-form', 'product-policy'],
        ],

        'signature' => [
            'enabledTenants' => 'default',
        ]
    ],

    'tenants' => [
        'default' => [
            'ui' => [
                // *** Must be declared in MasterLayout components ***
                'header' => "Header",
                'navigation' => "Navigation",
            ],

            'tasks' => [
                'client' => [
                    'rules' => [
                        // Custom rules here
                    ]
                ],
                'survey' => [
                    'rules' => [
                        'client/birthdate' => 'required|date_format:Y-m-d',
                        "profile/familyMembers" => 'required|integer',
                        "profile/familyEarners" => 'required|integer',
                        "profile/cohabitants" => 'required|integer',
                        "profile/cohabitantsMinors" => 'required|integer',
                        //
                    ]
                ]
            ],

            'allowedIP' => ['**********',],
        ],

        'simply' => [
            'maxUsers' => env('__TODO__MAX_USERS'),
            'maxPipelines' => env('__TODO__MAX_PIPELINES'),
        ],

        'dorotea' => [
            // @TODO refactor to global (just above) templateOverrides config value
            'ui' => [
                // *** Must be declared in MasterLayout components ***
                'header' => "HeaderDorotea"
            ],
            'maxUsers' => env('DOROTEA_MAX_USERS'),
            'createUserOnLogin' => env('DOROTEA_CREATE_USERS_ON_LOGIN'),
            'abortSyncUsersOnError' => env('DOROTEA_ABORT_SYNC_ON_ERROR'),
            'maxPipelines' => env('DOROTEA_MAX_PIPELINES'),
            'allowedIP' => explode(",", env('DOROTEA_LOGIN_IPS')),

            'tasks' => [
                'survey' => [
                    'rules' => [
                        'client/birthdate' => 'required|date_format:Y-m-d|after:date0|before:date1',
                        'profile/status' => 'required|in:married,single,divorced,widow',
                        "profile/familyMembers" => 'required|integer',
                        "profile/familyEarners" => 'required|integer',
                        "profile/cohabitants" => 'required|integer',
                        "profile/cohabitantsMinors" => 'required|integer',
                        "profile/realEstate" => 'required',
                        "profile/smoker" => 'required',
                        "profile/extremeSports" => 'required',
                        "profile/job" => 'required',

                        // @FIXME
                        //"profile.job.other" => 'required',

                        //"profile/areaAssets" => 'required',
                        //"profile/areaHouse" => 'required',
                        //"profile/areaPerson" => 'required',
                        "profile/budget" => 'required',
                        "profile/choice" => 'required',
                        /*"profile/currentInsuranceCar" => 'required',
                        "profile/currentInsuranceDeath" => 'required',
                        "profile/currentInsuranceHouse" => 'required',
                        "profile/currentInsuranceIllness" => 'required',
                        "profile/currentInsuranceInjury" => 'required',
                        "profile/currentInsuranceOther" => 'required',
                        "profile/currentInsuranceRC" => 'required',
                        "profile/currentMortgage" => 'required',*/
                        "profile/length" => 'required',
                        "profile/mortgage" => 'required',
                    ]
                ]
            ]
        ]
    ],

    'values' => [
        'tenants' => [
            'default' => [
                'jobs' => [
                    'job1' => 'asdasd',
                    'job2' => 'asdasd',
                ],
                'length' => [
                    'short' => [
                        'label' => "Breve termine (fino a 5 anni)",
                        'min' => 0,
                        'max' => 5,
                    ],
                    'medium' => [
                        'label' => "Medio termine (tra 6 e 10 anni)",
                        'min' => 6,
                        'max' => 10,
                    ],
                    'long' => [
                        'label' => "Lungo termine (oltre 10 anni)",
                        'min' => 10,
                        'max' => INF,
                    ],
                ]
            ],

            'dorotea' => [
                'jobs' => [
                    'job1' => 'asdasd',
                    'job2' => 'asdasd',
                ],
                'length' => [
                    'short' => [
                        'label' => "Breve termine (fino a 5 anni)",
                        'min' => 0,
                        'max' => 5,
                    ],
                    'medium' => [
                        'label' => "Medio termine (tra 6 e 10 anni)",
                        'min' => 6,
                        'max' => 10,
                    ],
                    'long' => [
                        'label' => "Lungo termine (oltre 10 anni)",
                        'min' => 10,
                        'max' => INF,
                    ],
                ]
            ]
        ],

            
    ],

    'rules' => [
        // This is for default rules NOT FOR DEFAULT TENANT!
        'default' => [
            'client' => [
                'person' => [
                    'person.name' => 'required|string|max:255',
                    'person.lastname' => 'required|string|max:255',
                    
                    'person.taxCode' => [
                        'required',
                        'string',
                        'max:16',
                        new UniqueInTenant(new Person(), 'taxCode'),
                    ],
                    
                    'person.birthdate' => 'required|date_format:Y-m-d',
                    'person.birthplace' => [
                        'required',
                        'string',
                        new ExistsInTenant(new Zip(), 'denominazione_ita'),
                    ],
                    
                    'person.email' => [
                        'required',
                        'email',
                        new UniqueInTenant(new Person(), 'email'),
                    ],

                    'person.phonePrefix' => 'required|regex:/^\+\d{1,3}$/',

                    'person.phone' => [
                        'required',
                        'digits_between:9,16',
                        new UniqueInTenant(new Person(), 'phone'),
                    ],

                    'person.addresses.residence.type' => 'required|string|in:residence',
                    'person.addresses.residence.street' => 'required|string',
                    'person.addresses.residence.number' => 'required|string',
                    
                    'person.addresses.residence.zip' => [
                        'required',
                        'string',
                        new ExistsInTenant(new Zip(), 'cap'),
                    ],

                    'person.addresses.residence.city' => 'required|string',
                    'person.addresses.residence.province' => 'required|string',
                    'person.addresses.residence.region' => 'required|string',
                    //'person.addresses.residence.country' => 'required|string|size:2',
                ],

                'enterprise' => [
                    'enterprise.name' => [
                        'required',
                        'string',
                        'max:255',
                        new UniqueInTenant(new Enterprise(), 'name'),
                    ],
                    
                    'enterprise.vat' => [
                        'required',
                        'string',
                        'size:11',
                        new UniqueInTenant(new Enterprise(), 'vat'),
                    ],

                    'enterprise.addresses.headquarters.type' => 'required|string|in:headquarters',
                    'enterprise.addresses.headquarters.street' => 'required|string',
                    'enterprise.addresses.headquarters.number' => 'required|string',

                    'enterprise.addresses.headquarters.zip' => [
                        'required',
                        'string',
                        new ExistsInTenant(new Zip(), 'cap'),
                    ],

                    'enterprise.addresses.headquarters.city' => 'required|string',
                    'enterprise.addresses.headquarters.province' => 'required|string',
                ],

                'identityDocument' => [
                    'person.documents.id.type' => 'required|string|in:id,passport,driving_license',
                    'person.documents.id.file' => [
                        'required',
                        'file',
                        'mimes:pdf,jpg,jpeg,png',
                        'max:2048', // 2MB
                    ],
                    'person.documents.id.number' => [
                        'required',
                        'string',
                        'max:255',
                    ],
                    'person.documents.id.issuer' => [
                        'required',
                        'string',
                        'max:255',
                    ],
                    'person.documents.id.issuerCountry' => [
                        'required',
                    ],
                    'person.documents.id.issuerDate' => [
                        'required',
                        'date_format:Y-m-d',
                    ],
                    'person.documents.id.expiry' => [
                        'required',
                        'date_format:Y-m-d',
                    ],

                ]
            ],
        ]
    ],

    'pipelines' => [
        
        //'dorotea' => [...], 
        
        'default' => [
            'tasks' => [

                [
                    'type' => 'client', 
                    'dependson' => null,

                    // Possible options: as defined in PipelineManager.
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Client\ClientController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Client\ClientManager', 
                    'template' => 'Tasks/Client',
                    'name' => 'Anagrafica',

                    'config' => [
                        'compile' => ['mup', 'privacy'],
                    ]
                ],
                [
                    'type' => 'signature', 
                    'dependson' => 'client',

                    // Possible options: as defined in PipelineManager.
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
                    'template' => 'Tasks/Signature',

                    'config' => [
                        'service' => IGSign::class,
                        'signatureDeadlineInDays' => 3,
                        'signatureType' => SignatureManager::$SIGNATURE_SIMPLE, // or SignatureManager::$SIGNATURE_FEA
                        'folderTitle' => 'Documentazione precontrattuale',
                        'folderDescription' => 'Documentazione precontrattuale',
                        'documents' => ['privacy'],
                        'attachments' => ['mup'],
                        'finalizationEvents' => [PrivacySigned::class],
                    ]
                ],
                [
                    'type' => 'survey', 
                    'dependson' => 'client',

                    // Possible options: as defined in PipelineManager.
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyManager', 
                    'template' => 'Tasks/Survey',
                ],
                [
                    'type' => 'mapper', 
                    'dependson' => 'survey',
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager', 
                    'template' => 'Tasks/ProductMapper',
                    
                    'config' => [
                        'compile' => ['demands-and-needs'],
                    ]
                ],
                [
                    // FEA 2
                    'type' => 'signature', 
                    'dependson' => 'survey',

                    // Possible options: as defined in PipelineManager.
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
                    'template' => 'Tasks/Signature',

                    'config' => [
                        'service' => IGSign::class,
                        'signatureDeadlineInDays' => 3,
                        'signatureType' => SignatureManager::$SIGNATURE_FEA,
                        'folderTitle' => 'Demands and Needs',
                        'folderDescription' => 'Demands and Needs',
                        'documents' => ['demands-and-needs'],
                        'attachments' => null,
                    ]
                ],
                [
                    'type' => 'issuance', 
                    'dependson' => 'signature',

                    // Possible options: as defined in PipelineManager.
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Issuance\IssuanceController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Issuance\IssuanceManager', 
                    'template' => 'Tasks/Issuance',

                    'config' => [
                        // 'compile' is implicit here, it's done natively by the IssuanceManager.
                        // maybe add some mechanism to check in task.finalize that all documents are finalized.
                    ]
                ],
                // FEA 3
                [
                    'type' => 'signature', 
                    'dependson' => 'issuance',

                    // Possible options: as defined in PipelineManager.
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
                    'template' => 'Tasks/Signature',

                    'config' => [
                        // 'signers' => ['contractor'], maybe should be in the Document / SignatureBatch architeture.
                        'service' => IGSign::class,
                        'signatureDeadlineInDays' => 3,
                        'signatureType' => SignatureManager::$SIGNATURE_FEA, // or SignatureManager::$SIGNATURE_FEA
                        'folderTitle' => 'Documentazione contrattuale',
                        'folderDescription' => 'Documentazione contrattuale',
                        'documents' => ['product-policy'],
                        'attachments' => [],

                        // maybe dump this one.
                        // @check
                        'dataInjection' => 'getDataForDocument'
                    ]
                ]
            ],
        ],
        'dorotea' => [
            'tasks' => [
                [
                    'type' => 'survey', 
                    'dependson' => null,

                    // Possible options: as defined in PipelineManager.
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyManager', 
                    'template' => 'Tasks/Dorotea/Survey',
                ],
                [
                    'type' => 'mapper', 
                    'dependson' => 'survey',
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager', 
                    'template' => 'Tasks/Dorotea/ProductMapper'
                ],
                [
                    'type' => 'summary', 
                    'dependson' => 'mapper',
                    'navigation' => 'pipeline.open|pipeline.closed',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Summary\SummaryController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Summary\SummaryManager', 
                    'template' => 'Tasks/Dorotea/Summary'
                ],
            ],
        ],
        'vertua' => [
            'tasks' => [
                [
                    'type' => 'survey', 
                    'dependson' => null,

                    // Possible options: as defined in PipelineManager.
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyManager', 
                    'template' => 'Tasks/Dorotea/Survey',
                ],
                [
                    'type' => 'mapper', 
                    'dependson' => 'survey',
                    'navigation' => 'pipeline.open',
                    'controller' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager', 
                    'template' => 'Tasks/Dorotea/ProductMapper'
                ],
                [
                    'type' => 'summary', 
                    'dependson' => 'mapper',
                    'navigation' => 'pipeline.open|pipeline.closed',
                    'controller' => 'Upnovation\Easyprofile\Tasks\Summary\SummaryController', 
                    'manager' => 'Upnovation\Easyprofile\Tasks\Summary\SummaryManager', 
                    'template' => 'Tasks/Summary'
                ],
            ],
        ],
    ],
];