<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\RadioOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'CFMUTUIEVOL',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates/products',
        'filename' => 'cf-mutui-evolution-06-2025.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'product-form',
        'title' => 'CF Mutui Evolution',
        'version' => '1.0.0',
        'description' => "Scheda Raccolta dati CF Mutui Evolution",
        'processor' => PdfProcessor::class,
        'signers' => ["contractor"],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],
        'overlayArray' => [
            new UserOverlay(1, 40, 42, ['properties' => ['name', 'lastname'],]),
            new TextOverlay(1, 144, 42, [], '@todo phone'),
            new SubjectOverlay(1, 37, 60, [
                'role' => 'contractor',
                'properties' => ['lastname'],
            ]),
            new SubjectOverlay(1, 120, 60, [
                'role' => 'contractor',
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 38.5, 64.5, [
                'role' => 'contractor',
                'method' => 'getPrintableBirthdate',
            ]),
            new TextOverlay(1, 110, 64.5, [], "@todo"),
            new ArrayOverlay(1, 110, 70, [
                'key' => 'quote',
            ]),

            // just a test here - configuration required.
            /*new RadioOverlay(1, 100, 70, [
                'key' => 'option',
                'options' => [
                    '5-1' => new TextOverlay(1, 67.5, 168.5, [], "x"),
                    '2' => new TextOverlay(1, 76, 100.5, [], "x"),
                    '3' => new TextOverlay(1, 80, 75, [], "x"),
                    '4' => new TextOverlay(1, 90, 75, [], "x"),
                ],
            ]),*/
            
        ],
    ],

    'policy' => [
        'title' => 'CF Mutui Evolution - Proposta assicurativa',
        'signers' => ["contractor"], 
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ]
        ],
    ],

    'quote' => [
        'net' => true,

        'fields' => [
            'combinazione',
            'capitale',
            'durata',
        ],
    ],

    'formRules' =>[
        'combinazione' => 'required|integer',
        'capitale' => 'required|numeric',
        'durata' => 'required|integer',
    ]
];