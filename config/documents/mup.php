<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'mup',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates',
        'filename' => 'mup-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'mup',
        'title' => "Allegato 3 (MUP)",
        'version' => '1.0.0',
        'description' => "Allegato 3 (Modello Unico Precontrattuale)",
        'processor' => PdfProcessor::class,
        'signers' => null,    
        'signatures' => null,
        'overlayArray' => [
            new UserOverlay(1, 52, 100, [
                'properties' => ['lastname', 'name'],
            ]),
            new TextOverlay(1, 70, 104, [], '@rui'),
            new TextOverlay(1, 101, 104, [], '@ruidate'),
            new TextOverlay(1, 63, 112, [], '@int1'),
            new TextOverlay(1, 115, 112, [], '@rui1'),
            new TextOverlay(1, 147, 112, [], '@ruidate'),
        ],
    ],
];