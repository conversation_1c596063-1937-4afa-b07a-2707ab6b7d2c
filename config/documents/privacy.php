<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'privacy',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates',
        'filename' => 'privacy-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'privacy',
        'title' => "Informativa Privacy",
        'version' => '1.0.0',
        'description' => "Informativa sull'utilizzo dei dati personali",
        'processor' => PdfProcessor::class,
        'signers' => ['contractor'],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 2,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],
        'overlayArray' => [
            new SubjectOverlay(1, 39, 34, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['name', 'lastname'],
            ]),
            new SubjectOverlay(1, 39, 37.5, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'contractor',
                        'individual' => null,
                    ],
                ],
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 28, 41, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['taxCode'],
            ]),
            new SubjectOverlay(1, 120, 41, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'contractor',
                        'individual' => null,
                    ],
                ],
                'properties' => ['vat'],
            ]),
            new SubjectOverlay(1, 63, 44.6, [
                'role' => 'contractor',
                'method' => 'getSubjectAddress',
            ]),
            new TextOverlay(2, 34, 71, [], 'x'),
            new TextOverlay(2, 33.5, 105, [], 'x'),
            new TextOverlay(2, 33, 156, [], 'x'),
        ],
    ],
];