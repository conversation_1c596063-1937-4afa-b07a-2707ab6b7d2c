<script>
import { useForm } from '@inertiajs/vue3'
import MasterLayout from '../Layouts/MasterLayout.vue'
import Card from 'primevue/card'
import FileUpload from 'primevue/fileupload'

export default {
    props: {
        pipeline: Object,
        subjects: Array,
        files: Array,
        products: Array,
        issuances: Array,
    },
    components: {
        MasterLayout,
        Card,
        FileUpload
    },
    setup() {
        return {
            form: useForm({
                file: null,
                issuance_id: null,
            }),
        }
    },
    methods: {
        submit(form) {
            form.post(`/tasks/issuance/${this.pipeline.id}/${this.pipeline.currentTask.id}/upload`, {
                forceFormData: true,
                
                onFinish: () => {
                    this.$inertia.reload({ preserveState: true, preserveScroll: true });
                }
            });
        }
    },
}
</script>

<template>
    <MasterLayout title="Scheda Posizione">
        <div class="flex flex-col gap-5">
            <div class="grid grid-cols-12 gap-6">
                <div class="col-span-4 flex flex-col gap-4">
                    <Card>
                        <template #title>
                            Soggetti
                        </template>
                        <template #content>
                            <div v-for="client in subjects" class="mb-4">
                                <h3 v-if="client.role == 'contractor'">
                                    <span class="flex items-center gap-2">
                                        <span class="inline-flex items-center justify-center w-7 h-7 rounded-full bg-blue-100">
                                            <i class="pi pi-user text-blue-500 text-lg"></i>
                                        </span>
                                        Contraente
                                    </span>
                                </h3>
                                <div v-if="client.person" class="mt-2">
                                    {{ client.person.name }}
                                    {{ client.person.lastname }}<br>
                                    {{ client.person.taxCode }}<br>
                                    {{ client.person.phone }}<br>
                                    {{ client.person.email }}
                                </div>
                            </div>
                        </template>
                    </Card>
                    <Card>
                        <template #title>Documenti</template>
                        <template #content>
                            <div v-for="file in files" :key="file.id" class="mb-2">
                                <i class="pi pi-file-pdf"></i>
                                <a :href="`/documents/${file.uuid}`" target="_blank">
                                    <template v-if="file.type == 'folder'">
                                        <span>{{ file.displayName }}</span> <br>
                                        <small>Archivio firmato</small>
                                    </template>
                                    <template v-else-if="file.document?.type == 'product-policy'">
                                        <span>{{ file.document.title }}</span>
                                        <small>Proposta da firmare</small>
                                    </template>
                                    <template v-else>
                                        <span>{{ file.document?.title }}</span> <br>
                                        <small v-if="file.document">{{ file.document.description }}</small>
                                        <span v-else>???</span>
                                    </template>
                                </a>
                            </div>
                            
                        </template>
                    </Card>
                    <Card>
                        <template #title></template>
                        <template #content>
                            <pre class="text-sm">{{ pipeline }}</pre>
                        </template>
                    </Card>
                </div>
                <div class="col-span-8">
                    <Card>
                        <template #title>Colonna Destra</template>
                        <template #content>
                            <template v-if="pipeline.currentTask">
                                <h3 class="mb-4">
                                    Attività in corso:
                                    {{ pipeline.currentTask.displayName }}
                                </h3>
                            </template>
                            <template v-if="pipeline.state == 'closed'">
                                <h3 class="mb-4">Posizione completata</h3>
                            </template>

                            <hr class="my-4">

                            <template v-if="pipeline.currentTask?.type == 'signature'">
                                <pre v-if="0">{{ pipeline.currentTask.data }}</pre>
                                <div v-if="pipeline.currentTask.data?.signatureStatus == 'pending'" class="mb-4">
                                    Il fascicolo è stato inviato al cliente per la firma in data 
                                    {{ pipeline.currentTask.data?.folder?.updated_at }}.<br>
                                    Scadenza fascicolo: 
                                    {{ pipeline.currentTask.data?.folder?.expires_at }}.
                                </div>
                            </template>

                            <template v-if="pipeline.currentTask?.type == 'issuance' && issuances.length">
                                <div v-for="issuance in issuances" :key="issuance.id" class="mb-4">
                                    <h3>
                                        {{ issuance.id }} - {{ issuance.product.name }} {{ issuance.status }}
                                    </h3>
                                    <form v-if="   debug = true || issuance.status == 'awaiting'" @submit.prevent="submit(form)" enctype="multipart/form-data">
                                        <input
                                            type="file"
                                            class="p-inputtext p-component"
                                            @change="e => {form.file = e.target.files[0]; form.issuance_id = issuance.id}"
                                            placeholder="File"
                                        />

                                            <button type="submit" class="p-button p-button-primary">Invia</button>
                                    </form>
                                </div>
                            </template>
                            
                        </template>
                    </Card>
                </div>
            </div>
        </div>
    </MasterLayout>
</template>
