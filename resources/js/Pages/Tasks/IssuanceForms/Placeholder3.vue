<script>
import PipelineLayout from '../../../Layouts/PipelineLayout.vue'
import Button from 'primevue/button'
import Message from 'primevue/message'
import InputText from 'primevue/inputtext'
import { useForm } from '@inertiajs/vue3'
import FormMessage from '../../../Components/Form/FormMessage.vue'
import InputNumber from 'primevue/inputnumber'
import RadioButton from "primevue/radiobutton"
import DatePicker from "primevue/datepicker";

export default {
    components: {
        PipelineLayout,
        Button,
        Message,
        InputText,
        FormMessage,
        InputNumber,
        RadioButton,
        DatePicker
    },
    props: {
        pipeline: Object,
        task: Object,
        issuance: Object,
    },
    data() {
        // Recupera i dati di default dal task se presenti
        const initial = this.task?.data?.issuance?.[this.issuance.product.id] || {}

        return {
            form: useForm({
                // Base
                combinazione: null,
                intermediazione: null,
                importoIntermediazione: 0,
                emissione: 0,

                // Mutuo
                importoFinanziato: 0,
                banca: '',
                durata: 0,
                dataErogazione: null,
                notaio: '',
            }),
            loading: false,
        }
    },
    methods: {
        submit() {
            this.loading = true;

            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.issuance.id}`, {
                onFinish: () => { this.loading = false }
            })
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <form @submit.prevent="submit">
                        <h2 class="flex items-center gap-4">
                            <img
                                :src="'/assets/companies/' + issuance.product.company.logo"
                                alt="Logo"
                                class="h-10 w-10 rounded-full"
                            >
                            <span>
                                {{ issuance.product.name }}
                                <p class="text-gray-500 text-sm font-normal">Form di adesione</p>
                            </span>
                        </h2>

                        <hr class="my-4">

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Seleziona la combinazione</div>
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-1" name="combinazione" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-1">Combinazione 1</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-3" name="combinazione" value="3" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-3">Combinazione 3</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-5" name="combinazione" value="5" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-5">Combinazione 5</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-6" name="combinazione" value="6" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-6">Combinazione 6</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-8" name="combinazione" value="8" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-8">Combinazione 8</label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Intermediazione:</div>
                            <div class="mt-1 space-x-6">
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.intermediazione" inputId="interm-1" name="intermediazione" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="interm-1">Si</label>
                                </div>
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.intermediazione" inputId="interm-0" name="intermediazione" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="interm-0">No</label>
                                </div>
                            </div>
                        </div>

                        <div v-if="form.intermediazione === '1'">

                            <div class="flex space-x-4 mb-8">
                                <div>
                                    <div class="font-semibold leading-6 text-gray-900">Importo Intermediazione</div>
                                    <InputNumber
                                        v-model="form.importoIntermediazione"
                                        class="w-full mb-4"
                                        mode="currency"
                                        currency="EUR"
                                        locale="it-IT"
                                    />
                                </div>

                                <div>
                                    <div class="font-semibold leading-6 text-gray-900">Diritti di emissione</div>
                                    <InputNumber
                                        v-model="form.emissione"
                                        class="w-full mb-4"
                                        mode="currency"
                                        currency="EUR"
                                        locale="it-IT"
                                    />
                                </div>
                            </div>

                        </div>

                        <div class="section-header mb-2 mt-14">Dati mutuo:</div>

                        <div class="grid grid-cols-2 gap-x-5 gap-y-8">
                            <div class="col-span-1">
                                <div class="font-semibold leading-6 text-gray-900">Importo finanziato</div>
                                <InputNumber
                                    v-model="form.importoFinanziato"
                                    class="w-full"
                                    mode="currency"
                                    currency="EUR"
                                    locale="it-IT"
                                />
                            </div>
                            <div class="col-span-1">
                                <div class="font-semibold leading-6 text-gray-900">Banca erogante</div>
                                <InputText
                                    v-model="form.banca"
                                    class="w-full"
                                />
                            </div>
                            <div class="col-span-1">
                                <div class="font-semibold leading-6 text-gray-900">Durata</div>
                                <InputNumber
                                    v-model="form.durata"
                                    class="w-full"
                                />
                            </div>
                            <div class="col-span-1">
                                <div class="font-semibold leading-6 text-gray-900">Data erogazione</div>
                                <DatePicker v-model="form.dataErogazione" dateFormat="dd/mm/yy" />
                            </div>
                            <div class="col-span-1">
                                <div class="font-semibold leading-6 text-gray-900">Notaio</div>
                                <InputText
                                    v-model="form.notaio"
                                    class="w-full"
                                />
                            </div>
                        </div>

                        <div>

                            <FormMessage :errors="errors" field="combinazione">Opzione</FormMessage>

                            <InputNumber
                                v-model="form.capitale"
                                placeholder="Capitale"
                                class="w-full mb-4"
                                mode="currency"
                                currency="EUR"
                                locale="it-IT"
                            />
                            <FormMessage :errors="errors" field="capitale">Capitale</FormMessage>

                            <InputNumber
                                v-model="form.durata"
                                placeholder="Durata (mesi)"
                                class="w-full mb-4"
                                mode="decimal"
                                :min="1"
                            />
                            <FormMessage :errors="errors" field="durata">Durata (mesi)</FormMessage>
                            
                        </div>

                        <div class="mt-6">
                            <Button type="submit" label="Conferma pacchetti" :loading="loading" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </PipelineLayout>
</template>
