<script>
import PipelineLayout from '../../../Layouts/PipelineLayout.vue'
import Button from 'primevue/button'
import Message from 'primevue/message'
import InputText from 'primevue/inputtext'
import { useForm } from '@inertiajs/vue3'
import FormMessage from '../../../Components/Form/FormMessage.vue'
import InputNumber from 'primevue/inputnumber'

export default {
    components: {
        PipelineLayout,
        Button,
        Message,
        InputText,
        FormMessage,
        InputNumber,
    },
    props: {
        pipeline: Object,
        task: Object,
        issuance: Object,
    },
    data() {
        // Recupera i dati di default dal task se presenti
        const initial = this.task?.data?.issuance?.[this.issuance.product.id] || {}

        return {
            form: useForm({
                product: this.issuance.product.id,
                option: initial.option ?? null,
                fee: initial.fee ?? null,
                applyFee: initial.applyFee ?? false,
                health: {
                    hospitalization: initial.health?.hospitalization ?? null,
                    hospitalizationW: initial.health?.hospitalizationW ?? null,
                    pathologies: initial.health?.pathologies ?? null,
                    void: initial.health?.void ?? null,
                    disability: initial.health?.disability ?? null,
                },
            }),
            loading: false,
            packages: [
                {
                    label: 'ITTo PII € 375 + ITP € 11.250',
                    value: 1,
                    prices: { 5: 1035, 10: 2070 },
                },
                {
                    label: 'ITTo PII € 500 + ITP € 15.000',
                    value: 2,
                    prices: { 5: 1380, 10: 2760 },
                },
                {
                    label: 'ITTo PII € 750 + ITP € 22.500',
                    value: 3,
                    prices: { 5: 2070, 10: 4140 },
                },
                {
                    label: 'ITTo PII € 900 + ITP € 27.000',
                    value: 4,
                    prices: { 5: 2475, 10: 4950 },
                },
                {
                    label: 'ITTo PII € 1.125 + ITP € 33.750',
                    value: 5,
                    prices: { 5: 3105, 10: 6210 },
                },
            ],
            healthQuestions: [
                { key: 'hospitalization', text: "E' stato/a ricoverato/a in istituto di cura negli ultimi cinque anni (salvo che per: conseguenze traumatiche di lesioni di arti, appendicectomia, ernia inguinale, varicocele, adentonsillectomia, emorroidectomia, colecistectomia senza postumi, varici arti inferiori, alluce valgo, meniscectomia, chirurgia estetica, parto)?" },
                { key: 'hospitalizationW', text: "E' in attesa di ricovero, di essere sottoposto/a a terapia continuativa e di avere malattie in atto, escluse quelle stagionali?" },
                { key: 'pathologies', text: "Soffre o ha sofferto di tumori, ipertensione cronica, angina pectoris, attacco ischemico transitorio, ictus, infarto del miocardio, epatite, cirrosi epatica, diabete, malattie renali o genitourinarie croniche, malattie croniche dell'apparato respiratorio o neurologiche o croniche dell'apparato muscoloscheletrico, infezioni da HIV?" },
                { key: 'void', text: "Sono state annullate polizze per i medesimi rischi qui assicurati da altre Compagnie Assicurative?" },
                { key: 'disability', text: "Percepisce pensione o assegno d’invalidità superiore al 20% per infortunio o malattia?" },
            ],
        }
    },
    methods: {
        submit() {
            this.loading = true
            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.issuance.id}`, {
                onFinish: () => { this.loading = false }
            })
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <form @submit.prevent="submit">
                        <h2 class="flex items-center gap-4">
                            <img
                                :src="'/assets/companies/' + issuance.product.company.logo"
                                alt="Logo"
                                class="h-10 w-10 rounded-full"
                            >
                            <span>
                                {{ issuance.product.name }}
                                <p class="text-gray-500 text-sm font-normal">Form di adesione</p>
                            </span>
                        </h2>

                        <hr class="my-4">

                        <div class="space-y-4">
                            <h2>Seleziona l'opzione</h2>
                            <div class="border rounded overflow-hidden">
                                <div class="grid grid-cols-3 font-semibold bg-gray-100 border-b">
                                    <div class="p-2">Durata</div>
                                    <div class="p-2 text-center">5 anni</div>
                                    <div class="p-2 text-center">10 anni</div>
                                </div>
                                <div
                                    v-for="pkg in packages"
                                    :key="pkg.value"
                                    class="grid grid-cols-3 border-b last:border-b-0 transition-colors"
                                    :class="[
                                        pkg.value % 2 === 1 ? 'bg-gray-50' : '',
                                        form.option === `5-${pkg.value}` || form.option === `10-${pkg.value}` ? 'bg-green-100 border-green-400' : ''
                                    ]"
                                >
                                    <div class="p-2 flex items-center">
                                        {{ pkg.label }}
                                    </div>
                                    <div class="p-2 text-center">
                                        <label class="inline-flex items-center">
                                            <input
                                                type="radio"
                                                :value="`5-${pkg.value}`"
                                                v-model="form.option"
                                                name="option"
                                            />
                                            <span class="ml-2">€ {{ pkg.prices[5] }}</span>
                                        </label>
                                    </div>
                                    <div class="p-2 text-center">
                                        <label class="inline-flex items-center">
                                            <input
                                                type="radio"
                                                :value="`10-${pkg.value}`"
                                                v-model="form.option"
                                                name="option"
                                            />
                                            <span class="ml-2">€ {{ pkg.prices[10] }}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <FormMessage :form="form" :errors="form.errors" field="option">Scegli una delle opzioni disponibili</FormMessage>

                            <hr class="my-4">

                            <h2>Intermediazione</h2>
                            
                            <div class="flex gap-8 items-center mb-2">
                                <label class="inline-flex items-center">
                                    <input
                                        type="radio"
                                        :value="true"
                                        v-model="form.applyFee"
                                        name="intermediation"
                                    />
                                    <span class="ml-2">Sì</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input
                                        type="radio"
                                        :value="false"
                                        v-model="form.applyFee"
                                        name="intermediation"
                                    />
                                    <span class="ml-2">No</span>
                                </label>
                            </div>

                            <div v-if="form.applyFee">
                                <InputNumber v-model="form.fee" mode="currency" currency="EUR" placeholder="Inserisci l'importo" />
                                <FormMessage :form="form" :errors="form.errors" field="fee">Importo intermediazione</FormMessage>
                            </div>

                            <hr class="my-4">

                            <h2>Questionario sanitario</h2>
                            <div class="border rounded overflow-hidden mb-6">
                                <div v-for="question in healthQuestions" :key="question.key" class="grid grid-cols-2 items-center border-b last:border-b-0 px-4 py-3">
                                    <div>
                                        {{ question.text }}
                                    </div>
                                    <div class="flex gap-8 justify-end">
                                        <label class="inline-flex items-center">
                                            <input
                                                type="radio"
                                                :name="`health_${question.key}`"
                                                :value="true"
                                                v-model="form.health[question.key]"
                                            />
                                            <span class="ml-2">Sì</span>
                                        </label>
                                        <label class="inline-flex items-center">
                                            <input
                                                type="radio"
                                                :name="`health_${question.key}`"
                                                :value="false"
                                                v-model="form.health[question.key]"
                                            />
                                            <span class="ml-2">No</span>
                                        </label>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <Button type="submit" label="Conferma pacchetti" :loading="loading" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </PipelineLayout>
</template>
