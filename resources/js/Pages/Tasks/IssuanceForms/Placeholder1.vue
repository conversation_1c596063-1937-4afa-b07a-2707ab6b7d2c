<script>
import PipelineLayout from '../../../Layouts/PipelineLayout.vue'
import <PERSON><PERSON> from 'primevue/button'
import Message from 'primevue/message'
import { useForm } from '@inertiajs/vue3'

export default {
    components: {
        PipelineLayout,
        Button,
        Message,
    },
    props: {
        pipeline: Object,
        task: Object,
        product: Object,
    },
    data() {
        return {
            form: useForm({
                product: this.product?.id ?? null,
                packages: null,
                duration: null,
            }),
            loading: false,
        }
    },
    methods: {
        submit() {
            this.loading = true
            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.product.id}`, {
                onFinish: () => { this.loading = false }
            })
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <form @submit.prevent="submit">
                        <h2 class="flex items-center gap-4">
                            <img
                                :src="'/assets/companies/' + product.company.logo"
                                alt="Logo"
                                class="h-10 w-10 rounded-full"
                            >
                            <span>
                                {{ product.name }}
                                <p class="text-gray-500 text-sm font-normal">Form di adesione</p>
                            </span>
                        </h2>

                        <hr class="my-4">

                        <div v-if="form.errors.packages" class="text-red-500 text-sm mb-2">
                            {{ form.errors.packages }}
                        </div>

                        <div class="space-y-4">
                            <h2>Pacchetti disponibili</h2>
                            <!-- Pacchetto 1 -->
                            <div class="border rounded flex">
                                <div class="flex items-start p-3">
                                    <input type="radio" class="mt-1 mr-2"
                                        :value="1"
                                        v-model="form.packages"
                                        name="packages"
                                    />
                                </div>
                                <div class="flex-1 grid grid-cols-3 border-l">
                                    <div class="p-3">
                                        <strong>Pacchetto 1</strong><br>
                                        <span class="italic">Attivabile da:<br>Tutti gli Aderenti/Assicurati</span>
                                    </div>
                                    <div class="p-3 border-l">
                                        Decesso
                                    </div>
                                    <div class="p-3 border-l">
                                        Capitale Assicurato
                                    </div>
                                </div>
                            </div>
                            <!-- Pacchetto 2 -->
                            <div class="border rounded flex">
                                <div class="flex items-start p-3">
                                    <input type="radio" class="mt-1 mr-2"
                                        :value="2"
                                        v-model="form.packages"
                                        name="packages"
                                    />
                                </div>
                                <div class="flex-1 grid grid-cols-3 border-l">
                                    <div class="p-3">
                                        <strong>Pacchetto 2</strong><br>
                                        <span class="italic">Attivabile solo da:<br>Tutti gli Aderenti/Assicurati</span>
                                    </div>
                                    <div class="p-3 border-l">
                                        Decesso<br>
                                        Invalidità Totale Permanente<br>
                                        <span class="text-xs">(da Infortunio o Malattia)</span>
                                    </div>
                                    <div class="p-3 border-l">
                                        Capitale Assicurato<br>
                                        Capitale Assicurato
                                    </div>
                                </div>
                            </div>
                            <!-- Pacchetto 3 -->
                            <div class="border rounded flex">
                                <div class="flex items-start p-3">
                                    <input type="radio" class="mt-1 mr-2"
                                        :value="3"
                                        v-model="form.packages"
                                        name="packages"
                                    />
                                </div>
                                <div class="flex-1 grid grid-cols-3 border-l">
                                    <div class="p-3">
                                        <strong>Pacchetto 3</strong><br>
                                        <span class="italic">Attivabile solo da:<br>Lavoratori Dipendenti Privati</span>
                                    </div>
                                    <div class="p-3 border-l">
                                        Decesso<br>
                                        Perdita d’Impiego Involontaria
                                    </div>
                                    <div class="p-3 border-l">
                                        Capitale Assicurato<br>
                                        Indennità mensile assicurata
                                    </div>
                                </div>
                            </div>
                            <!-- Pacchetto 4 -->
                            <div class="border rounded flex">
                                <div class="flex items-start p-3">
                                    <input type="radio" class="mt-1 mr-2"
                                        :value="4"
                                        v-model="form.packages"
                                        name="packages"
                                    />
                                </div>
                                <div class="flex-1 grid grid-cols-3 border-l">
                                    <div class="p-3">
                                        <strong>Pacchetto 4</strong><br>
                                        <span class="italic">Attivabile solo da:<br>Lavoratori Autonomi<br>Lavoratori Dipendenti Pubblici<br>Non Lavoratori</span>
                                    </div>
                                    <div class="p-3 border-l">
                                        Decesso<br>
                                        Inabilità Totale Temporanea<br>
                                        <span class="text-xs">(da Infortunio o Malattia)</span><br>
                                        Ricovero Ospedaliero<br>
                                        <span class="text-xs">(da Infortunio o Malattia)</span>
                                    </div>
                                    <div class="p-3 border-l">
                                        Capitale Assicurato<br>
                                        Indennità mensile assicurata<br>
                                        Indennità mensile assicurata
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="border mt-8 rounded overflow-hidden">
                            <div class="bg-cyan-200 border-b px-4 py-2 font-semibold text-center">
                                Durata del contratto
                            </div>
                            <div class="px-4 py-3 flex gap-8 items-center">
                                <label class="flex items-center gap-2">
                                    <input
                                        type="radio"
                                        name="duration"
                                        :value="5"
                                        v-model="form.duration"
                                    />
                                    5 anni
                                </label>
                                <label class="flex items-center gap-2">
                                    <input
                                        type="radio"
                                        name="duration"
                                        :value="10"
                                        v-model="form.duration"
                                    />
                                    10 anni <span class="text-xs text-gray-600">(solo in caso di scelta del Pacchetto 1 o del Pacchetto 2)</span>
                                </label>
                            </div>
                            <div class="px-4 pb-3 text-sm text-gray-700">
                                Alla scadenza quinquennale o decennale il contratto non potrà più essere rinnovato.
                            </div>
                        </div>

                        <div class="mt-6">
                            <Button type="submit" label="Conferma pacchetti" :loading="loading" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </PipelineLayout>
</template>
