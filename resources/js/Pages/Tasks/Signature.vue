<script>
import PipelineLayout from '../../Layouts/PipelineLayout.vue';
import Message from 'primevue/message';
import {router} from "@inertiajs/core";
import { ref } from 'vue';

import But<PERSON> from 'primevue/button';

const env = import.meta.env;

export default {
    props: {
        pipeline: Object,
        task: Object,
        signers: Array,
        documents: Array,
        foo: Object,
        folderId: Number,
    },

    components: {
        PipelineLayout,
        Button,
        Message,
    },

    data() {
        return {
            loading: ref(false),
        }
    },

    setup() {
        return {
            env
        }
    },  

    methods: {
        next: function() {
            this.loading = true;
            
            router.get(`/pipeline/${this.pipeline.id}/next`, {}, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    this.loading = false;
                },
                onError: () => {
                    this.loading = false;
                },
            });
        },
    }
}

</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <div>
                        <h2 class="mb-2">Documenti nel fascicolo</h2>
                        <ul v-for="file in documents">
                            <li class="">
                                <i class="pi pi-file-pdf"></i>
                                <a :href="`/documents/${file.uuid}`" target="_blank">{{ file.document.title }}</a>
                            </li>
                        </ul>
                    </div>
                    <hr class="my-4">
                    <div>
                        <h2>Firmatari per questo step</h2>
                        <div v-for="signer in signers" :key="signer.id">
                            {{ signer.name }} {{ signer.lastname }}<br>
                            {{ signer.email }}<br>
                            {{ signer.phone }}<br>
                        </div>
                    </div>
                    <div v-if="foo?.credentialsData">Ho trovato {{ foo.credentialsData.length }} certificati</div>
                    <hr class="my-4">
                    <div>
                        <h2>Firma elettronica</h2>
                        <div v-if="! task.data.signatureStatus" class="my-3 p-3 text-sm rounded border border-gray-200 bg-gray-50">
                             Questo step richiede
                            <span v-if="task.config.signatureType == 'fea'">firma elettronica avanzata</span>
                            <span v-if="task.config.signatureType == 'simple'">firma semplice</span>.
                            <br>
                            N.B.: Per procedere allo step successivo è necessario attendere che la firma sia completata.
                        </div>

                        <div v-if="task.data.signatureStatus" class="my-3 p-3 text-sm rounded border border-gray-200 bg-gray-50">
                            <p v-if="task.data.signatureStatus == 'done'" class="text-green-600">Fascicolo firmato</p>
                            <p v-if="task.data.signatureStatus == 'fail'" class="text-green-600">Fascicolo fallito o scaduto</p>
                            <p v-if="task.data.signatureStatus == 'pending'" class="text-yellow-600">
                                In attesa della firma cliente<br>
                                <a target="_blank" :href="`/signature/${pipeline.id}/${task.id}/callback/igs/${task.data.folder.id}/11`">Test callback SUCCESS</a>
                            </p>
                        </div>
                        <div v-if="! task.data.signatureStatus || task.data.signatureStatus == ''" class="mt-3">
                            <!--
                            <a v-if="task.config.signatureType == 'fea'" :href="`/signature/${pipeline.id}/${task.id}/cert`">FEA (con anagrafica registrata)</a>
                            <a v-if="task.config.signatureType == 'fea'" :href="`/signature/${pipeline.id}/${task.id}/cert?debug`">FEA (forza utente debug)</a>
                            -->
                            <a
                                :href="`/sign/${pipeline.id}/${task.id}`"
                                class="p-button p-component p-button-info">
                                <i class="pi pi-file-edit"></i>
                                Invia i documenti per la firma
                            </a>
                            
                        </div>

                        <div v-else>
                            <a v-if="folderId" :href="`/signature/${pipeline.id}/${task.id}/callback/igs/${folderId}/11`">Test callback SUCCESS</a><br>

                            <a v-if="env.DEV || env.mode == 'staging'"
                                :href="`/sign/${pipeline.id}/${task.id}`"
                                class="p-button p-component p-button-info">
                                <i class="pi pi-file-edit"></i>
                                Forza nuova firma (Modalità di test)
                            </a>
                        </div>

                        <template v-if="env.DEV || env.mode == 'staging'">
                            <br>
                            <a
                                :href="`/sign/fake/${task.id}`"
                                class="p-button p-component p-button-info mt-3">
                                <i class="pi pi-file-edit"></i>
                                Firma fake
                            </a>
                        </template>
                            
                        
                    </div>

                    
                </div>

                
                
            </div>

            <div class="flex justify-between">
                <Button type="button" label="Indietro" :disabled="true"></Button>
                <Button type="button" label="Avanti" @click="next()" :loading="loading" :disabled="task.data.signatureStatus != 'done'"></Button>
            </div>
            
        </div>

    </PipelineLayout>
</template>
