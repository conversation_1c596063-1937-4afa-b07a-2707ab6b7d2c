<script>
import PipelineLayout from '../../Layouts/PipelineLayout.vue';
import { router } from "@inertiajs/core";
import { ref } from 'vue';
import Button from 'primevue/button';
import Message from 'primevue/message';
import ToggleSwitch from 'primevue/toggleswitch';
import Tooltip from 'primevue/tooltip';

const env = import.meta.env;

export default {
    props: {
        pipeline: Object,
        task: Object,
        issuances: Array,
    },

    components: {
        PipelineLayout,
        Button,
        Message,
        ToggleSwitch,
    },

    directives: {
        tooltip: Tooltip,
    },

    data() {
        return {
            loading: ref(false),
            selectedProduct: null,
            hoveredProduct: null,
            toggled: {},
            processType: {
                'direct': 'Immediato',
                'deferred': 'Differito',
                'download': 'Download',
            }
        }
    },

    setup() {
        return {
            env
        }
    },  

    methods: {
        next: function() {
            this.loading = true;
            
            router.get(`/pipeline/${this.pipeline.id}/next`, {}, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    this.loading = false;
                },
                onError: () => {
                    this.loading = false;
                },
            });
        },
        toggleProduct(issuanceId) {
            this.toggled[issuanceId] = !this.toggled[issuanceId];
        },
        go: function(issuanceId) {
            router.get(`/issue/${this.pipeline.id}/${this.task.id}/${issuanceId}`, {}, {
                preserveState: true,
                preserveScroll: true,
            });
        },
        getProcessTypeTooltip(type) {
            switch (type) {
                case 'direct':
                    return 'Per polizze ad adesione. Compila il modulo e procedi con la firma al cliente.';
                case 'deferred':
                    return 'Processo differito. Compila il modulo web e attendi il caricamento della proposta assicurativa da parte del backoffice.';
                case 'download':
                    return 'Scarica, compila e ricarica il pdf compilabile, poi attendi il caricamento della proposta assicurativa da parte del backoffice.';
            }
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">

            <div class="card w-full">
                <div class="card-body !p-6">
                    <h2 class="mb-2">Prodotti selezionati</h2>
                    <Message severity="info" class="mb-4">
                        Completa il processo per ognuno dei prodotti elencati prima di passare allo step di firma elettronica.
                    </Message>
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="bg-gray-100">
                                <th width="25%" class="text-left px-3 py-2">Compagnia</th>
                                <th class="text-left px-3 py-2">Nome prodotto</th>
                                <th class="text-left px-3 py-2">Tipo di processo</th>
                                <th class="text-left px-3 py-2">Stato</th>
                                <th class="text-left px-3 py-2">Includi</th>
                                <th class="text-left px-3 py-2"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="issuance in issuances" :key="issuance.id">
                                <td class="px-3 py-2 align-middle">
                                    <div class="flex items-center gap-3">
                                        <img :src="'/assets/companies/' + issuance.product.company.logo" alt="" class="h-8 w-8 rounded-full">
                                        <span>{{ issuance.product.company.name }}</span>
                                    </div>
                                </td>
                                <td class="px-3 py-2 align-middle">
                                    <a class="m-0" :href="`/tasks/issuance/${pipeline.id}/${task.id}/product/${issuance.product.id}`">
                                        {{ issuance.product.name }}
                                    </a>
                                </td>
                                <td class="px-3 py-2 align-middle text-left">
                                    
                                    <Button
                                        icon="pi pi-info-circle"
                                        class="p-button-text p-button-rounded p-button-info mr-2"
                                        v-tooltip.top="getProcessTypeTooltip(issuance.product.processType)"
                                        style="font-size: 1.1rem;"
                                        aria-label="Info prodotto"
                                    />
                                    <span class="mr-2">{{ processType[issuance.product.processType] }}</span>
                                </td>
                                <td class="px-3 py-2 align-middle text-middle">
                                    
                                    <span v-if="issuance.status === 'completed'" class="text-green-600">
                                        <Button
                                            icon="pi pi-info-circle"
                                            class="p-button-text p-button-rounded p-button-info mr-2"
                                            v-tooltip.top="'Emissione completata'"
                                            style="font-size: 1.1rem;"
                                            aria-label="Info prodotto"
                                        />
                                        Completato
                                    </span>
                                    <span v-if="issuance.status === 'awaiting'" class="text-yellow-600">
                                        <Button
                                            icon="pi pi-info-circle"
                                            class="p-button-text p-button-rounded p-button-info mr-2"
                                            v-tooltip.top="'Attendi che il backoffice carichi la proposta assicurativa'"
                                            style="font-size: 1.1rem;"
                                            aria-label="Info prodotto"
                                        />
                                        Attesa caricamento proposta
                                    </span>
                                    <span v-if="issuance.status === 'pending'" class="text-red-600">
                                        <Button
                                            icon="pi pi-info-circle"
                                            class="p-button-text p-button-rounded p-button-info mr-2"
                                            v-tooltip.top="'Completa il processo per questo prodotto'"
                                            style="font-size: 1.1rem;"
                                            aria-label="Info prodotto"
                                        />
                                        Non completato
                                    </span>
                                </td>
                                <td class="px-3 py-2 align-middle text-middle">
                                    <ToggleSwitch
                                        v-model="toggled[issuance.id]"
                                        :trueValue="true"
                                        :falseValue="false"
                                        class="align-middle"
                                    />
                                </td>
                                <td class="px-3 py-2 align-middle text-right">
                                    <Button
                                        type="button"
                                        label="Compila"
                                        class="p-button-sm"
                                        severity="contrast"
                                        :disabled="issuance.product.processType == 'deferred' && issuance.status !== 'pending'"
                                        @click="go(issuance.id)"
                                        
                                    >
                                    </Button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="flex justify-end">
                <Button v-if="task.data['taskCompleted']" type="button" label="Avanti" @click="next()" :loading="loading"></Button>
                <Button v-else type="button" label="Avanti" @click="next()" :loading="loading" disabled v-tooltip.top="'Completa il processo per ognuno dei prodotti elencati prima di passare allo step successivo.'"></Button>
            </div>
            
        </div>
    </PipelineLayout>
</template>
