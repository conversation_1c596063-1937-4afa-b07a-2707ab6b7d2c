<script setup>
import MasterLayout from '../Layouts/MasterLayout.vue';
import { dateFormatter } from "@/dateFormatter";
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputText from 'primevue/inputtext';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import { ref } from 'vue';

defineProps({
    clients: Array,
    enterprises: Array 
})

const filters = ref({
    'global': { value: '', matchMode: 'contains' },
    'name': { value: '', matchMode: 'contains' },
    'lastname': { value: '', matchMode: 'contains' },
    'birthdate': { value: '', matchMode: 'contains' },
    'pipeline': { value: '', matchMode: 'contains' }
})

const enterpriseFilters = ref({
    'global': { value: '', matchMode: 'contains' },
    'name': { value: '', matchMode: 'contains' },
    'vat': { value: '', matchMode: 'contains' },
    'pipeline': { value: '', matchMode: 'contains' }
})
</script>

<template>
    <MasterLayout title="Clienti">
        <div class="card">
            <div class="card-header">
                <h1 class="mb-0">Clienti</h1>
            </div>
            <div class="card-body">
                <TabView>
                    <TabPanel header="Persone fisiche">
                        <div class="overflow-x-auto">
                            <DataTable 
                                :value="clients"
                                v-model:filters="filters" 
                                filterDisplay="menu" 
                                :globalFilterFields="['name', 'lastname', 'birthdate', 'pipeline']"
                                paginator 
                                :rows="50" 
                                tableStyle="min-width: 50rem" 
                                class="text-sm">

                                <template #header>
                                    <div class="flex justify-end">
                                        <IconField>
                                            <InputIcon>
                                                <i class="pi pi-search" />
                                            </InputIcon>
                                            <InputText v-model="filters['global'].value" placeholder="Cerca ovunque..." />
                                        </IconField>
                                    </div>
                                </template>

                                <Column field="id" header="#" sortable></Column>
                                <Column field="privacy" header="Privacy" sortable>
                                    <template #body="rowData">
                                        <span v-if="rowData.data.privacy" class="text-green-500">
                                            <i class="pi pi-check" />
                                        </span>
                                        <span v-else class="text-yellow-500">
                                            <i class="pi pi-hourglass" />
                                        </span>
                                    </template>
                                </Column>
                                <Column field="birthdate" header="Data di nascita" sortable>
                                    <template #body="rowData">
                                        {{ dateFormatter(rowData.data.birthdate) }}
                                    </template>
                                </Column>
                                <Column field="name" header="Nome" sortable></Column>
                                <Column field="lastname" header="Cognome" sortable></Column>
                                <Column field="pipeline" header="N. Posizioni" sortable>
                                    <template #body="rowData">
                                        {{ rowData.data.pipeline?.length ?? 0 }}
                                    </template>
                                </Column>
                            </DataTable>
                        </div>
                    </TabPanel>

                    <TabPanel header="Aziende">
                        <div class="overflow-x-auto">
                            <DataTable 
                                :value="enterprises"
                                v-model:filters="enterpriseFilters" 
                                filterDisplay="menu" 
                                :globalFilterFields="['name', 'vat', 'pipeline']"
                                paginator 
                                :rows="50" 
                                tableStyle="min-width: 50rem" 
                                class="text-sm">

                                <template #header>
                                    <div class="flex justify-end">
                                        <IconField>
                                            <InputIcon>
                                                <i class="pi pi-search" />
                                            </InputIcon>
                                            <InputText v-model="enterpriseFilters['global'].value" placeholder="Cerca ovunque..." />
                                        </IconField>
                                    </div>
                                </template>

                                <Column field="id" header="#" sortable></Column>
                                <Column field="vat" header="P.IVA" sortable></Column>
                                <Column field="name" header="Ragione Sociale" sortable></Column>
                                <Column field="rep" header="Legale Rappresentante" sortable>
                                    <template #body="rowData">
                                        {{ rowData.data.rep?.name ?? '-' }}
                                        {{ rowData.data.rep?.lastname ?? '-' }}
                                    </template>
                                </Column>
                            </DataTable>
                        </div>
                    </TabPanel>
                </TabView>
            </div>
        </div>
    </MasterLayout>
</template>