<script>
import MasterLayout from '../Layouts/MasterLayout.vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import { ref } from 'vue';

const filters = ref({
    'global': {
        value: '',
        matchMode: 'contains'
    },
    // tood: add more filters
})

export default {
    props: {
        docs: Array
    },

    data() {
        return {
            list: ref(this.docs),
            filters: filters
        }
    },

    components: {
        MasterLayout,
        DataTable,
        Column,
        InputText,
        Button,
    }
}
</script>

<template>
    <MasterLayout title="Documenti">

        <div class="card">

            <div class="card-header">
                <h1 class="mb-0">Documenti</h1>
            </div>

            <div class="card-body">
                <DataTable :value="list" paginator :rows="10" class="text-sm">
                    <Column field="id" header="ID" sortable></Column>
                    <Column field="node.name" header="Nodo rete" sortable></Column>
                    <Column field="title" header="Titolo" sortable>
                        <template #body="data">
                            <span>{{ data.data.title }}</span><br>
                            <small>{{ data.data.description }}</small>
                        </template>
                    </Column>
                    <Column field="version" header="Versione" sortable></Column>
                    <Column field="link" header="Azioni">
                        <template #body="data">
                            <a
                                :href="`/documents/${data.data.id}/template`"
                                class="btn btn-sm mr-1"
                                title="Visualizza"
                            >
                                <span class="pi pi-search"></span>
                            </a>
                            <a
                                :href="`/documents/${data.data.id}/template?download`"
                                class="btn btn-sm"
                                title="Scarica"
                            >
                                <span class="pi pi-download"></span>
                            </a>
                        </template>
                    </Column>
                </DataTable>
            </div>
        </div>
    </MasterLayout>
</template>