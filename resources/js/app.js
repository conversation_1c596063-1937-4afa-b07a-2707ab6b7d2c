

import './bootstrap';
import { createApp, h } from 'vue'
import { createInertiaApp } from '@inertiajs/vue3'
import VueTippy from 'vue-tippy'
import 'tippy.js/dist/tippy.css'
import PrimeVue from "primevue/config";
import Aura from '@primevue/themes/aura';
import ToastService from 'primevue/toastservice';

createInertiaApp({
  resolve: name => {
    const pages = import.meta.glob('./Pages/**/*.vue', { eager: true })
    return pages[`./Pages/${name}.vue`]
  },
  setup({ el, App, props, plugin }) {
    createApp({ render: () => h(App, props) })
      .use(plugin, VueTippy)
      .use(PrimeVue, {
            theme: {
                preset: Aura,
                options: {
                    darkModeSelector: 'none' // This disables dark mode
                }
            }
        })
      .use(ToastService)
      .mount(el)
  },
})
