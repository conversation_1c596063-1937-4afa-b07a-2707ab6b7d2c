<script>
import AutoComplete from 'primevue/autocomplete';
import InputText from 'primevue/inputtext';
import Select from 'primevue/select';
import axios from 'axios';
import { ref } from 'vue';
import FormMessage from './FormMessage.vue';
import { has } from 'lodash';

export default {
    props: {
        address: Object,
        cssClass: Object,
        type: {
            type: String,
            default: null
        },
        errors: Object,
    },

    components: {
        InputText,
        AutoComplete,
        Select,
        FormMessage,
    },

    data() {
        return {
            geo: ref({}),

            addressType: [
                {code: 'residence', name: 'Residenza'}, 
                {code: 'headquarters', name: 'Sede Legale'},
                {code: 'birthplace', name: '<PERSON><PERSON> di nascita'},
            ],

            suggestions: ref([]),

            showType: true,
        }
    },

    computed: {
        // Devo filtrare gli errori. Il controller (Laravel) si aspetta
        // che i campi in input seguano la forma dei json inviati,
        // ovvero person.addresses.TIPOADDRESS. 
        // e di conseguenza usa la stessa nomenclatura per gli errori.
        // per rendere il validatore compatibile con diversi tipo di indirizzi,
        // e compatibile anche con gli altri campi "singoli" (es. nome, cognome, ecc),
        // è stato necessario passare il nome del campo completamente quaquificato
        filteredErrors() {
            if (! this.errors || ! this.errors.messages || ! this.errors.key) {
                return {};
            }

            return Object.fromEntries(
                Object.entries(this.errors.messages).filter(([key]) =>
                    key.startsWith(this.errors.key)
                )
            );
        }
    },

    mounted() {
        if (this.type && this.addressType.map(t => t.code).includes(this.type)) {
            this.address.type = this.type;
            this.showType = false;

            return;
        }
    },

    watch: {
        geo: {
            deep: true,
            handler(newGeo) {
                if (newGeo) {
                    this.address.zip = newGeo.cap || '';
                    this.address.city = newGeo.denominazione_ita || '';
                    this.address.province = newGeo.sigla_provincia || '';
                    this.address.region = newGeo.denominazione_regione || '';
                }
            }
        }
    },

    methods: {
        async search(event) {
            if (! event.query.length || event.query.length < 3) {
                this.suggestions = [];
                return;
            }

            await axios.get(`/geo/${event.query}`).then(response => {
                this.suggestions = response.data;
            });
        },

        async searchMunicipality(event) {
            if (! event.query.length || event.query.length < 3) {
                this.suggestions = [];
                return;
            }

            await axios.get(`/geo/${event.query}?_`).then(response => {
                this.suggestions = response.data;
            });
        },

        hasErrors() {
            return this.filteredErrors && Object.keys(this.filteredErrors).length;
        }
    }
}
</script>

<template>
    
    <div :class="{'error-border rounded-lg p-5': hasErrors()}">
        <!-- Indirizzo -->
        <div class="flex flex-col md:w-1/2" v-show="showType">
            <div class="flex gap-1">
                <Select v-model="address.type" :options="addressType" optionLabel="name" optionValue="code" placeholder="Tipo di indirizzo"  />
            </div>
            <div class="text-xs p-1">Scegli il tipo di indirizzo.</div>  
        </div>

        <div class="flex flex-col md:w-1/2" v-show="address.type !== 'birthplace'">
            <div class="flex gap-1">
                <InputText class="md:w-10/12" v-model="address.street" aria-describedby="username-help" placeholder="Via/Largo/Piazza" />
                <InputText class="md:w-2/12" v-model="address.number" aria-describedby="username-help" placeholder="N. Civico" />
            </div>

            <FormMessage :errors="filteredErrors" :field="`${errors?.key}.street`">Inserisci l'indirizzo.</FormMessage>
        </div>

        <!-- CAP autocomplete -->
        <div>
            <AutoComplete optionLabel="cap" v-model="geo" :suggestions="suggestions" @complete="search" placeholder="CAP">
                <template #option="slotProps">
                    <div>
                        <div>{{ slotProps.option.denominazione_ita }}</div>
                        <small>{{ slotProps.option.cap }}</small>
                    </div>
                </template>
            </AutoComplete>

            <FormMessage :errors="filteredErrors" :field="`${errors?.key}.zip`" customMessage="Devi selezionare uno dei valori suggeriti nella dropdown.">Seleziona il CAP.</FormMessage> 
        </div>

        <!-- GEO -->
        <div class="flex flex-col md:w-1/2">
            <div class="flex gap-1">
                <InputText readonly disabled class="md:w-4/12" v-model="geo.denominazione_ita" aria-describedby="" placeholder="Comune" />
                <InputText readonly disabled class="md:w-2/12" v-model="geo.sigla_provincia" aria-describedby="" placeholder="Provincia" />
                <InputText readonly disabled class="md:w-6/12" v-model="geo.denominazione_regione" aria-describedby="" placeholder="Regione" />
            </div>
            <div class="text-xs p-1">Comune, provincia e regione.</div>  
        </div>
    </div>
    
</template>
