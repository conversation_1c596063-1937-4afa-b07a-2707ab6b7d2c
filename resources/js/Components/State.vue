<script setup>
import Badge from './Badge.vue';

defineProps({
    pipeline: Object
});
</script>

<template>
    <Badge :text="formatBadge('text')" :color="formatBadge('color')"></Badge>
</template>

<script>

export default {
    data() {
        return {
            open : {
                text: 'Aperta',
                color: 'warning',
            },
            closed : {
                text: 'Chiusa',
                color: 'success',
            },
            canceled : {
                text: 'Cancellata',
                color: 'danger',
            }
        }
    },
    methods: {
        formatBadge(type) {
            return this[this.pipeline.state][type];
        }
    }
}
</script>