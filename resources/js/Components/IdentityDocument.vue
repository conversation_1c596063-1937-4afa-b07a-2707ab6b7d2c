<script>
import InputText from "primevue/inputtext";
import FormMessage from "./Form/FormMessage.vue";
import Select from "primevue/select";
import FileUpload from "primevue/fileupload";
import { ref } from "vue";
import { router } from '@inertiajs/vue3';
import <PERSON><PERSON> from "primevue/button";
import FormUtils from "../form.utils";
import AutoComplete from "primevue/autocomplete";

export default {
    components: {
        InputText,
        FormMessage,
        Select,
        FileUpload,
        Button,
        AutoComplete,
    },
    props: {
        errors: Object,
        document: Object,
        type: {
            type: String,
            default: null
        },
        exclude: {
            type: Array,
            default: () => []
        },
    },
    data: function () {
        let docTypes = [
            { code: 'id', name: 'Carta d\'identità' },
            { code: 'passport', name: 'Passaporto' },
            { code: 'driving_license', name: 'Patente di guida' },

            // Health insurance card secondo gpt :/
            { code: 'hic', name: 'Tessera sanitaria / Codice fiscale' }
        ];

        if (this.type) {
            docTypes = docTypes.filter((docType) => docType.code == this.type);
        }

        if (this.exclude.length > 0) {
            docTypes = docTypes.filter((docType) => ! this.exclude.includes(docType.code));
        }

        return {
            docType: docTypes,

            utils: new FormUtils(),

            suggestions: ref([]),
        }
    },
    methods: {
        search(event) {
            if (!event.query || event.query.length < 3) {
                this.suggestions = [];
                return;
            }

            axios.get(`/geo/countries/${event.query}`).then(response => {
                this.suggestions = response.data;
            });
        },

        handleUploaded(event) {
            // In caso di upload, si può fare qualcosa qui
            // come mostrare un messaggio di successo o simili.
            console.log('Documento caricato:', event);
        },

        submit() {
            router.post('/documents', { document: this.document });
        }
    }
}
</script>

<template>
    <div class="flex flex-col md:w-1/2 gap-5">
        <div class="md:w-1/2">
        <Select
            :class="utils.errorCss(errors, 'person.documents.id.type')"
            v-model="document.type"
            :options="docType"
            optionLabel="name"
            optionValue="code"
            placeholder="Tipo di documento"
            style="width: 100%;"/>
            <FormMessage :errors="errors" field="person.documents.id.type">Seleziona il tipo di documento.</FormMessage>
        </div>

        <div>
            <InputText :class="utils.errorCss(errors, 'person.documents.id.number')" v-model="document.number" placeholder="Numero documento"></InputText>
            <FormMessage :errors="errors" field="person.documents.id.number">Inserisci numero del documento.</FormMessage>
        </div>

        <div>
            <InputText :class="utils.errorCss(errors, 'person.documents.id.issuer')" v-model="document.issuer" placeholder="Ente emittente"></InputText>
            <FormMessage :errors="errors" field="person.documents.id.issuer">Inserisci il nome dell'ente emittente.</FormMessage>
        </div>

        <div>
            <AutoComplete :class="utils.errorCss(errors, 'person.documents.id.issuerCountry')" optionLabel="name" v-model="document.issuerCountry" :suggestions="suggestions" @complete="search" placeholder="Paese emittente">
                <template #option="slotProps">
                    <div>
                        <div>{{ slotProps.option.name }}</div>
                        <small>{{ slotProps.option.code }}</small>
                    </div>
                </template>
            </AutoComplete>

            <FormMessage :errors="errors" field="person.documents.id.issuerCountry">Ricerca e seleziona il paese emittente</FormMessage>
        </div>

        <div>
            <input :class="utils.errorCss(errors, 'person.documents.id.issuerDate')" type="date" class="p-inputtext p-component" v-model="document.issuerDate" placeholder="Data di emissione" />
            <FormMessage :errors="errors" field="person.documents.id.issuerDate">Inserisci la data di emissione del documento</FormMessage>
        </div>
        <div>
            <input :class="utils.errorCss(errors, 'person.documents.id.expiry')" type="date" class="p-inputtext p-component" v-model="document.expiry" placeholder="Data di emissione" />
            <FormMessage :errors="errors" field="person.documents.id.expiry">Inserisci la data di scadenza del documento</FormMessage>
        </div>
        <div>
            <input :class="utils.errorCss(errors, 'person.documents.id.file')" type="file" @change="e => document.file = e.target.files[0]" class="p-inputtext p-component" placeholder="File" />
            <FormMessage :errors="errors" field="person.documents.id.file">Carica una fotografia o scansione fronte/retro del documento</FormMessage>
        </div>
    </div>
</template>
