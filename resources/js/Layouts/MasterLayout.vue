<script>

import Header from "@/Components/Header.vue";
import HeaderDorotea from "@/Components/HeaderDorotea.vue";
import Navigation from "@/Components/Navigation.vue";
import Footer from "@/Components/Footer.vue";
import SlideOver from "../Components/SlideOver.vue";
import {usePage} from "@inertiajs/vue3";
import 'primeicons/primeicons.css';
import Toast from "primevue/toast";
import { useToast } from 'primevue/usetoast';

export default {
    components: {
        Header,
        HeaderDorotea,
        Navigation,
        Footer,
        SlideOver,
        Toast,
    },

    props: {
        user: Object,
        navItems: Object,
        tech: Object,
        roles: Object,
        title: String,
        ui: Object,
    },

    setup() {
        // Required to access "in-page", shared global props
        // coming from HandleInertiaRequests.
        const page = usePage()

        let env = import.meta.env;

        return {
            env,
            "data": page.props,
        }
    },

    mounted() {
        const toast = useToast();
        const page = usePage();

        // Mostra toast se c'è un errore globale
        if (page.props.errors && page.props.errors.error) {
            toast.add({
                severity: 'error',
                summary: 'Errore',
                detail: page.props.errors.error + (page.props.errors.errorCode ? ` Codice errore: (${page.props.errors.errorCode})` : ''),
                life: 0
            });
        }
    }
}
</script>

<template>
    <div class="min-h-full">

        <Toast/>

        <!-- Debug Panel -->
        <SlideOver v-if="this.env.DEV || ['development', 'staging'].includes(this.env.MODE)" :data="{tech: this.data.tech, user: this.data.user}"></SlideOver>

        <component :is="this.data.ui.header" :user="this.data.user" :navigation="this.data.navItems"></component>

        <component :is="this.data.ui.navigation" :title="title"></component>

        <main>
            <div class="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                <slot>Loading...</slot>
            </div>
        </main>

        <Footer></Footer>

    </div>
</template>

