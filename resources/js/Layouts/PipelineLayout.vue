<script setup>
import MasterLayout from './MasterLayout.vue';
import {usePage} from "@inertiajs/vue3";
import {computed} from "vue";
import {ChevronRightIcon, CheckIcon} from "@heroicons/vue/24/outline";

defineProps({
    pipeline: Object,
    current: Object
})

const page = usePage()
const data = computed(() => page.props)
function formatDateTime (date) {
    return new Date(date).toLocaleDateString('it-IT');
}
</script>

<template>
    <MasterLayout :title="`Posizione #${data.pipeline.id} / Data creazione ${formatDateTime(data.pipeline.created_at)}`" :navItems="data.pipeline.tasks.map((i) => i.type)">
        <nav>
            <ol class="flex border-2 rounded-lg border-neutral-200 bg-white">
                <li v-for="(task, index) in data.pipeline.tasks" class="flex flex-1 relative">
                    <a class="flex items-center w-100" :href="task.accessible && data.pipeline.state == 'open' ? `/pipeline/${data.pipeline.id}/task/${task.id}` : null">
                        <span class="flex items-center font-semibold py-4 px-6">
                            <span class="flex shrink-0 h-10 w-10 rounded-full justify-center items-center border-2"
                                  :class="formatFormProgressColors(task.state, 'dot')">
                                <span v-if="task.state !== 'closed'" class="font-normal text-lg" :class="formatFormProgressColors(task.state, 'text')">{{ index + 1 }}</span>
                                <span v-if="task.state === 'closed'" class="text-white" ><CheckIcon class="h-6 w-6" /></span>
                            </span>
                            <span :class="formatFormProgressColors(task.state, 'text')" class="ml-2">{{ task.displayName }}</span>
                        </span>
                    </a>
                    <div class="absolute top-0 right-0 h-full w-5" v-if="index+1 !== data.pipeline.tasks.length">
                        <ChevronRightIcon class="w-5 h-full text-neutral-400" />
                    </div>
                </li>
            </ol>
        </nav>

        <hr class="py-4">

        <slot>Loading...</slot>

<!--        <a :href="`/pipeline/${data.pipeline.id}/next`">Next</a>-->

    </MasterLayout>

</template>

<script>
const stateColors = {
    "open" : {
        "dot": 'border-neutral-200',
        "text": 'text-neutral-500',
    },
    "progress" : {
        "dot": 'border-blue-600',
        "text": 'text-blue-600',
    },
    "closed" : {
        "dot": 'border-green-600 bg-green-600',
        "text": 'text-black',
    }
}
export default {
    methods:{
        formatFormProgressColors (state, type) {
            return stateColors[state][type];
        }
    }
}
</script>
