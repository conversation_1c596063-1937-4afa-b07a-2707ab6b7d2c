@tailwind base;
@tailwind components;

.theme-default {
    --color-primary: #059669;
    --color-primary-accent: #056d4c;
    --color-info: #0284c7;
    --color-info-accent: #026396;
    --color-secondary: transparent;
    --color-secondary-accent: #e2e8f0;
    --color-danger: #eb3838;
    --color-danger-accent: #bc2e2e;
}

.theme-dorotea {
    --color-primary: #059669;
    --color-primary-accent: #056d4c;
    --color-info: #0284c7;
    --color-info-accent: #026396;
    --color-danger: #eb3838;
    --color-danger-accent: #bc2e2e;
}

@tailwind utilities;

.btn {
    @apply border-2 rounded-md py-2 px-3 text-sm;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}
.btn:hover {
    /*@apply shadow-md;*/
}
.btn.btn-sm {
    @apply py-1 px-2 text-sm;
}
.btn.btn-lg {
    @apply py-3 px-4 text-lg;
}

.btn-primary {
    @apply text-white bg-primary;
}
.btn-primary:hover {
    @apply bg-primary-accent;
}

.btn-info {
    @apply text-white bg-info;
}
.btn-info:hover {
    @apply bg-info-accent;
}

.btn-secondary {
    @apply bg-secondary text-gray-800;
    border: 1px solid rgb(203,213,225);
}
.btn-secondary:hover {
    @apply bg-secondary-accent;
}

.btn-danger {
    @apply bg-danger text-gray-800;
    border: 1px solid rgb(203,213,225);
}
.btn-danger:hover {
    @apply bg-danger-accent;
}

.btn-icon {
    @apply inline-flex items-center;
}

.btn-icon.btn-icon-center {
    @apply inline-flex items-center justify-center;
}

h1 {
    @apply text-3xl font-bold mb-8 text-gray-900
}

h2 {
    @apply text-2xl font-bold text-gray-900
}

h4 {
    @apply font-bold mb-2 text-teal-600;
}

.card {
    @apply rounded-lg shadow-xl bg-white;
}

.card-header {
    @apply p-5 border-b;
}

.card-body {
    @apply p-5;
}

.card-footer {
    @apply border-t;
}

table tr:nth-child(even) {
    @apply bg-slate-100;
}

.badge-regular {
    @apply bg-gray-50 text-gray-600 ring-gray-500/10
}

.badge-danger {
    @apply bg-red-50 text-red-600 ring-red-500/10;
}

.badge-success {
    @apply bg-green-50 text-green-700 ring-green-600/20;
}

.badge-warning {
    @apply bg-yellow-50 text-yellow-700 ring-yellow-600/20;
}

.section-header {
    @apply text-2xl font-light text-sky-500;
}

/* Rimuove quei cazzo di inutili anelli dopo il click */
[type='checkbox']:hover, [type='checkbox']:focus, [type='radio']:hover, [type='radio']:focus {
    --tw-ring-offset-width: 0 !important;
    outline: none !important;
}

.loader {
    width: 60px;
    height: 60px;
    border: 8px solid #3ea3dc;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
}

.loader-mini {
    width: 15px;
    height: 15px;
    border: 2px solid #ffffff;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
}

@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.disabled input,
.disabled label {
    cursor: not-allowed;
    opacity: 0.5;
}

@layer utilities {
  .error-border {
    @apply !border-red-700 !border-2 !border-solid;
  }
}