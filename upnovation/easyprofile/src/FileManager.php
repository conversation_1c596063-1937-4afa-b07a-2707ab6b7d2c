<?php namespace Upnovation\Easyprofile;

use App\Models\File;
use App\Models\Pipeline;
use App\Models\Task;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FileManager
{
    /**
     * Save to database and file system.
     */
    public function save(string $tenant, File $file, $data) : File
    {
        if (! $tenant) {
            throw new \Exception("No tenant set for file saving.");
        }
        if (! Storage::disk($file->disk)->put("{$tenant}/{$file->path}/{$file->filename}", $data)) {
            throw new Exception("Error saving file to disk: {$file->disk}/{$tenant}/{$file->path}/{$file->filename}");
        }

        $file->save();

        return $file;
    }

    public function delete(File $file) 
    {
        DB::connection('tenant')->beginTransaction();
        
        $file->delete();
//dd('delete', $file);
        if (! Storage::disk($file->disk)->delete($file->getFullPath())) {
            throw new Exception("Error deleting file: {$file->getFullPath()}");
        }

        
        DB::connection('tenant')->commit();
    }

    public function deleteTaskSignableFiles(Task $task)
    {
        // Delete all files associated with the task
        $files = File::where('task_id', $task->id)->where('type', 'signable');

        $files = $files->get();

        foreach ($files as $file) {
            try {
                $this->delete($file);
            } catch (Exception $e) {
                Log::error("Error deleting file: {$file->getFullPath()} - " . $e->getMessage());
            }
        }
    }

    public function deletePipelineSignableFiles(Pipeline $pipeline)
    {
        // Delete all files associated with the pipeline
        $files = File::whereIn('task_id', function($query) use ($pipeline) {
            $query->select('id')
                ->from('tasks')
                ->where('pipeline_id', $pipeline->id);
        })->where('type', 'signable');

        $files = $files->get();

        foreach ($files as $file) {
            try {
                $this->delete($file);
            } catch (Exception $e) {
                Log::error("Error deleting file: {$file->getFullPath()} - " . $e->getMessage());
            }
        }
    }

    public function loadPipelineFiles(Task $task, array $attrs = [], array|null $documentTypes = []) : Collection
    {
        // Recupera tutti i file associati a task che appartengono alla stessa pipeline
        // e dove i documenti associati soddisfano gli attributi specificati
        $files = File::whereIn('task_id', function($query) use ($task) {
            $query->select('id')
                ->from('tasks')
                ->where('pipeline_id', $task->pipeline_id);
        });

        // Applica eventuali filtri aggiuntivi
        foreach ($attrs as $key => $value) {
            $files->where($key, $value);
        }
//dd($documentTypes);
        if (! empty($documentTypes)) {
            $files->whereHas('document', function($q) use ($documentTypes) {
                $q->whereIn('type', $documentTypes);
            });
        }

        return $files->with('document')->get();
    }   
}
