<?php namespace Upnovation\Easyprofile\Signature\IGSign;

use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\Signature\IGSign\IGSignClient;
use Upnovation\Easyprofile\Signature\SignatureBatch;
use Upnovation\Easyprofile\Signature\SignatureServiceInterface;

class IGSign /*implements SignatureServiceInterface*/
{
    /** @var IGSignClient */
    protected IGSignClient $ig;

    /** @var Time4MindClient */
    protected Time4MindClient $time4mind;

    public function __construct()
    {
        $this->ig = new IGSignClient(env('IGSIGN_ENDPOINT'), env('IGSIGN_TOKEN'));

        $this->time4mind = new Time4MindClient(
            env('TIME4MIND_RAO_USERNAME'),
            storage_path('app/private/keys/client.crt'),
            storage_path('app/private/keys/client.key'),
            storage_path('app/private/keys/ca.pem'),
        );
    }

    public function getCallbackUrl(Pipeline $pipeline, Task $task, int $folderId, int $eventId): string
    {
        if (in_array(env('APP_ENV'), ['local']))
        {
            return sprintf(
                env('IGSIGN_DEV_CALLBACK_URL'),
                $pipeline->id,
                $task->id,
                $folderId,
                $eventId
            );
        }

        return route('signature.callback.igsign', [
            'pipeline' => $pipeline, 
            'task' => $task,
            'folderId' => $folderId, 
            'eventId' => $eventId
        ]);
    }

    /**
     * Underlying method to handle the signing process.
     */
    protected function sign(SignatureBatch $batch, array $signers)
    {
        $deadline = $batch->deadline;

        // 1. Create folder
        $folder = $this->ig->createFolder(
            $batch->title,
            $batch->description,
            Carbon::now()->addDays($deadline)->format('Y-m-d')
        );

        if (! $folder || ! isset($folder->id)) {
            throw new \Exception('Failed to create folder in IGSign');
        }

        // Setup callbacks
        foreach([11, 13] as $eventId) {
            $this->setCallback($batch->task, $folder->id, $eventId);
        }

        // 2. Add documents to folder
        foreach ($batch->documents as $document) {
            $this->configureDocument($folder->id, $document, $signers);
        }

        foreach ($batch->attachments as $attachment) {
            $this->ig->uploadAttachment($folder->id, $attachment);
        }

        return $this->ig->startFolder($folder->id);
    }

    public function setCallback(Task $task, int $folderId, int $eventId) : array
    {
        $callbackUrl = $this->getCallbackUrl(
            $task->pipeline, 
            $task, 
            $folderId, 
            $eventId
        );

        if (! $callbackUrl) {
            throw new \Exception("Error building callback URL task: {$task->id}, folder: {$folderId}, event: {$eventId}");
        }

        $callback = $this->ig->registerCallback(
            $folderId, 
            $callbackUrl, 
            $eventId
        );

        if (! $callback) {
            throw new \Exception("Error registering callback for task: {$task->id}, folder: {$folderId}, event: {$eventId}");
        }

        if (isset($callback['statusCode']) && $callback['statusCode'] !== 200) {
            $errorCode = $callback['errorCode'] ?? 'unknown';

            throw new \Exception("Error registering callback. Code: {$errorCode}");
        }

        return $callback;
    }

    public function signSimple(SignatureBatch $batch, array $signers)
    {
        // @TODO CONFIG
        $this->ig
            ->setAuthType(IGSignClient::$AUTH_TYPE_EMAIL)
            ->setSignType(IGSignClient::$SIGN_TYPE_SIMPLE);

        return $this->sign($batch, $signers);
    }

    public function signFea(SignatureBatch $batch, array $signers)
    {
        if (! $this->time4mind->issueCertificate($signers[0])) {
            throw new \Exception('Failed to issue certificate for signer');
        }

         $this->ig
            ->setAuthType(IGSignClient::$AUTH_TYPE_EMAIL)
            ->setSignType(IGSignClient::$SIGN_TYPE_CERTIFICATE);

        return $this->sign($batch, $signers);
    }

    /**
     * Uploads a document to the IGSign folder and configures it for signing.
     */
    public function configureDocument(int $folderId, object $document, array $signers): void
    {
        $uploadedDocument = $this->ig->uploadDocument($folderId, $document);

        foreach ($signers as $signer) {
            if (! $signer instanceof Person) {
                throw new \InvalidArgumentException('Signer must be a Person');
            }

            // @TODO check step result
            $step = $this->ig->addSimpleSignatureStep(
                $folderId, 
                $uploadedDocument->id, 
                $signer
            );

            foreach ($document->signatures as $signature) {
                $this->ig->addVisibleSignature(
                    $folderId,
                    $uploadedDocument->id,
                    $step->user_id, 
                    $signature['page'],
                    $signature['x'],
                    $signature['y'],
                    $signature['cx'],
                    $signature['cy']
                 );
            }
        }
    }

    public function getFolder(int $folderId)
    {
        return $this->ig->getFolderStatus($folderId);
    }

    public function downloadFolder(int $folderId)
    {
        return $this->ig->downloadSignedFolder($folderId);
    }

    public function isFolderCompleted($folder): bool
    {
        return $folder && isset($folder['state']) && $folder['state'] == 4;
    }
}