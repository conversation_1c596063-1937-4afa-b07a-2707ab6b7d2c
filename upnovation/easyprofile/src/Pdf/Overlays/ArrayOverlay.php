<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;

class ArrayOverlay extends AbstractOverlay implements InjectableInterface
{
    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y);
     
        if (empty($settings) || empty($settings['key'])) {
            throw new \Exception("Settings cannot be empty for ArrayOverlay");
        }

        $this->settings = $settings;
    }

    public function inject($data)
    {
        $this->value = (array)$data;
    }

	public function getValue() : string
	{
        if (! isset($this->value[$this->settings['key']])) {
            throw new \Exception("Key does not exist in value for ArrayOverlay");
        }

        return $this->value[$this->settings['key']];
    }

	public function resolve(Pipeline $pipeline)
    {
        // Do nothing. Work with injected data.
    }

}