<?php namespace Upnovation\Easyprofile\Modules\Documents;

use App\Models\Document;
use App\Models\File;
use App\Models\Task;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class DocumentsManager
{
    public function getDocuments()
    {
        return Document
            ::with('node')
            ->where('type', '!=', 'product-policy')
            ->get();
    }

    public function getDocumentsByType(array $types)
    {
        return Document::with('node')->whereIn('type', $types)->get();
    }

    /**
     * Load documents as configured in 
     *      task.config.documents
     *      task.config.attachments
     * 
     * @return Collection
     */
    public function loadFromTaskConfiguration(Task $task) : Collection
    {
        $documents = new Collection();

        if (isset($task->config['documents'])) {
            $documents = $documents->merge(
                $this->getDocumentsByType($task->config['documents'])
            );
        }

        if (isset($task->config['attachments'])) {
            $documents = $documents->merge(
                $this->getDocumentsByType($task->config['attachments'])
            );
        }

        return $documents;
    }

    public function prepareDocuments(Task $task, Collection $documents)
    {
        Log::debug("Preparing documents for task: {$task->id}");

        foreach($documents as $documentTemplate) {
            Log::debug("Compiling document: {$documentTemplate->title} ({$documentTemplate->type}) using processor: {$documentTemplate->processor}");
            
            // Load processor.
            $processor = app()->make($documentTemplate->processor);

            // Compile document.
            $file = $processor->compile(
                $documentTemplate, 
                $task->pipeline,

                // Output file
                new File([
                    'filename' => "{$documentTemplate->type}-{$documentTemplate->version}-". (date('Ymd-His')) ."-{$task->pipeline_id}-{$task->id}.pdf",
                    'disk' => 'documents',
                    'path' => 'compiled',
                    'task_id' => $task->id,
                    'document_id' => $documentTemplate->id,
                    'type' => 'signable',
                ]),

                // Task might want to inject data in the processor
                $task->getInjectableData($documentTemplate)
            );

            // @fixme
            // why outside filemanager?
            $file->save();
        }
    }
}
