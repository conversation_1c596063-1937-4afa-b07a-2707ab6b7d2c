<?php namespace Upnovation\Easyprofile;

use App\Models\Task;

class Errors
{
    public static function make(Task $task, string $messageKey, ?int $ttl = 0) : array
    {
        if (! $message = config("easyprofile.errors.{$messageKey}")) {
            $message = "Si è verificato un errore imprevisto durante l'elaborazione della richiesta.";
        }

        $user = auth()->user();

        return [
            'error' => $message,
            'errorCode' => "{$task->pipeline->id}-{$task->id}-{$user->id}-" . date('YmdHis'),
            'ttl' => $ttl,
        ];
    }
}