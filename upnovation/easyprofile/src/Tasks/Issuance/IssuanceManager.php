<?php namespace Upnovation\Easyprofile\Tasks\Issuance;

use App\Events\IssuanceProcessed;
use App\Events\PolicyUploaded;
use App\Models\File;
use App\Models\Issuance;
use App\Models\Task;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use League\CommonMark\Extension\SmartPunct\Quote;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Quote\QuoteService;
use Upnovation\Easyprofile\Tasks\AbstractTask;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class IssuanceManager extends AbstractTask
{
    protected QuoteService $quoteService;

    public function __construct(QuoteService $quoteService, DocumentsManager $documentsManager, FileManager $fileManager)
    {
        parent::__construct($documentsManager, $fileManager);

        $this->quoteService = $quoteService;
    }

    public function initialize(Task $task, ?Task $previousTask = null): Task
    {
        $task = parent::initialize($task, $previousTask);

        $products = $task->pipeline->getSelectedProducts();

        DB::connection('tenant')->beginTransaction();

        // Delete all issuances for the task
        $task->issuances()->delete();

        // Create new issuance for each product
        foreach ($products as $product) {
            $task->issuances()->create([
                'product_id' => $product->id,
                'status' => 'pending',
            ]);
        }

        DB::connection('tenant')->commit();

        return $task;
    }

    public function getConfig(Issuance $issuance): array|null
    {
        $product = $issuance->product;

        if (! $document = $product->getFormDocument()) {
            Log::error("Product form document not found for product: {$product->name}");

            return null;
        }

        if (! $config = $document->loadConfiguration()) {
            Log::error("Document configuration not found for document: {$document->title}");

            return null;
        }

        return $config;
    }

    /**
     * Get the input from the user and process the issuance.
     * Direct: the user compile a form and the issuance pdf is compiled immediately.
     * Deferred: the user fill a form and the issuance pdf uploaded at backoffice.
     * 
     * Download: @todo the user downloads/reuploads the issuance pdf.
     */
    public function process(Task $task, Issuance $issuance, array $data)
    {
        switch ($issuance->product->processType) {
            case 'direct':
            case 'deferred':
                // Deferred seems to be the same as direct, so we can use the same method.
                return $this->processDirect($task, $issuance, $data);
        }

        throw new Exception("Unknown issuance process type: {$issuance->product->processType}");
    }

    public function processDirect(Task $task, Issuance $issuance, array $data)
    {
        if (! $config = $this->getConfig($issuance)) {
            throw new Exception("Issuance configuration not found for product: {$issuance->product->name}");
        }

        // Setup data.
        $product = $issuance->product;
        $document = $product->getFormDocument();

        // This product requires a quotation to be printed in the docs.
        if (isset($config['quote']) && isset($config['quote']['fields'])) {
            $parameters = Arr::only($data, $config['quote']['fields']);

            $quote = $this->quoteService->quote($product->code, $parameters);

            $data = array_merge($data, ['quote' => $quote['totale']]);
        } 

        // If issuance were already processed, we need to delete the previous files.
        if ($file = File::whereDocumentId($document->id)->whereTaskId($task->id)->first()) {
            $this->files->delete($file);
        }

        switch ($product->processType) {
            case 'direct':
                $fileType = 'signable';
                $issuance->status = 'completed';
                break;
            case 'deferred':
                $fileType = 'compiled';
                $issuance->status = 'awaiting';
                break;
            default:
                throw new \Exception("Unknown process type: {$product->processType}");
        }

        // Process file.
        /** @var PdfProcessor $processor */
        $processor = app()->make(PdfProcessor::class);

        $file = $processor->compile(
            $document,
            $task->pipeline,
            new File([
                'filename' => "{$document->type}-{$document->version}-". (date('Ymd-His')) ."-{$task->pipeline_id}-{$task->id}.pdf",
                'disk' => 'documents',
                'path' => 'compiled',
                'task_id' => $task->id,
                'document_id' => $document->id,
                'type' => $fileType,
            ]),
            $data
        );

        $file->save();

        $issuance->save();

        $this->updateTaskCompletion($task);

        // fire an event
        event(new IssuanceProcessed($issuance));

        return $issuance;
    }

    public function makePolicyFile(Issuance $issuance): File
    {
        if (! $issuance->product) {
            throw new Exception("Issuance product not found for issuance: {$issuance->id}");
        }

        $product = $issuance->product;

        if (! $document = $product->getPolicyDocument()) {
            throw new Exception("Product policy document not found for product: {$product->name}");
        }

        // Create file.
        return new File([
            'task_id' => $issuance->task_id,
            'document_id' => $document->id,
            'type' => 'signable',
            'disk' => 'documents',
            'path' => 'compiled',
            'filename' => "policy-" . date('Ymd-His') . "-{$issuance->task_id}-{$issuance->id}.pdf",
        ]);
    }
   
    public function savePolicyFile(Issuance $issuance, File $file, $rawData)
    {
        if ($issuance->status != 'awaiting') {
            throw new Exception("Issuance status is not 'awaiting': {$issuance->status}");
        }

        // We need to delete the form file that the user generated.
        $formDocument = $issuance->product->getFormDocument();
        $formFile = File::whereDocumentId($formDocument->id)->whereTaskId($issuance->task->id)->first();

        // @fixme sembra non cancellare questo
        if ($formFile) {
            $this->files->delete($formFile);
        }

        // We also want to delete any previous policy file.
        $policyDocument = $issuance->product->getPolicyDocument();
        $policyFile = File::whereDocumentId($policyDocument->id)->whereTaskId($issuance->task->id)->first();
        if ($policyFile) {
            $this->files->delete($policyFile);
        }

        // Now we save the new policy file.
        $this->files->save(
            app('currentTenant')->name, 
            $file, 
            $rawData
        );

        // Update the issuance status.
        $issuance->status = 'completed';
        $issuance->save();

        // Update the task completion.
        $this->updateTaskCompletion($issuance->task);

        event(new PolicyUploaded($issuance));

        return $issuance;
    }

    public function updateTaskCompletion(Task $task): Task
    {
        $completed = true;

        foreach ($task->issuances as $issuance) {
            if ($issuance->status != 'completed') {
                $completed = false;
                break;
            }
        }

        $task->addData('taskCompleted', $completed);
        $task->save();

        return $task;
    }

}
