<?php namespace Upnovation\Easyprofile\Tasks;

use App\Models\Task;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;

abstract class AbstractTask implements TaskInterface
{
    protected DocumentsManager $documents;

    protected FileManager $files;

    public function __construct(DocumentsManager $documents, FileManager $files)
    {
        $this->documents = $documents;

        $this->files = $files;
    }

    public function initialize(Task $task, ?Task $previousTask = null): Task
    {
        $this->files->deleteTaskSignableFiles($task);

        return $task;
    }

    public function finalize(Task $task): Task
    {
        $this->handleConfig($task);

        return $task;
    }

    public function handleConfig(Task $task) : Task
    {
        if (isset($task->config['compile'])) {
            $this->compileDocuments($task);
        }

        return $task;
    }

    public function compileDocuments(Task $task)
    {
        if (! isset($task->config['compile'])) {
            return null;
        }
        
        $documents = $this->documents->getDocumentsByType(
            $task->config['compile']
        );

        // @todo return what???
        $this->documents->prepareDocuments(
            $task, 
            $documents
        );
    }
}