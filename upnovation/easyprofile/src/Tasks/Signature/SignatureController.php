<?php namespace Upnovation\Easyprofile\Tasks\Signature;

use App\Http\Controllers\Controller;
use App\Models\Pipeline;
use App\Models\Task;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\Errors;
use Upnovation\Easyprofile\Signature\IGSign\IGSign;
class SignatureController extends Controller
{
    protected $service;

    protected SignatureManager $manager;

    public function __construct(SignatureManager $manager)
    {
        $this->manager = $manager;

        // @todo dependency injection for signature service
        //$this->service = app()->make(SignatureServiceInterface::class);
        $this->service = new IGSign();
    }

    public function getIndex(Pipeline $pipeline, Task $task)
    {
        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'signers' => $this->manager->getSigners($task),
            'documents' => $this->manager->loadFiles($task),
        ]);
    }

    public function getSign(Request $request, Pipeline $pipeline, Task $task)
    {
        // @todo check acl / gates



        if ($task->type !== 'signature') {
            throw new Exception("Task {$task->id} is not a signature task.");
        }

        try {
            $this->manager->processSignature($task);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            
            if (env('APP_DEBUG')) {
                throw $e;
            }

            return redirect()->back()->withErrors(Errors::make($task, 'signature'));
        }

        return redirect()->route('tasks.signature', [
            'pipeline' => $pipeline, 
            'task' => $task, 
        ]);
    }

    public function getIGSignCallback(Request $request, Pipeline $pipeline, Task $task, $folderId, $eventId)
    {
        $this->manager->processCallback($task, [
            'folderId' => $folderId,
            'eventId' => $eventId,
        ]);

        return response(200);
    }

}
