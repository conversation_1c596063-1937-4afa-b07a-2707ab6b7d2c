<?php namespace Upnovation\Easyprofile\Tasks\Client;

use App\Models\Address;
use App\Models\Client;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\Interfaces\Clientable;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Task;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Upnovation\Easyprofile\Tasks\AbstractTask;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class ClientManager extends AbstractTask
{
    public function bind(Pipeline $pipeline, Clientable $entity, string $role): Client | null
    {
        DB::connection('tenant')->beginTransaction();

        $client = new Client();
        $client->pipeline_id = $pipeline->id;
        $client->name = (string)$entity;
        $client->role = $role;

        if ($entity->getType() == 'individual') {
            $client->person_id = $entity->id;
        } elseif ($entity->getType() == 'legal') {
            $client->enterprise_id = $entity->id;
        } else {
            Log::error("Unknown entity type.");
        }

        $pipeline->type = $entity->getType();
        $pipeline->save();

        $client->save();

        DB::connection('tenant')->commit();

        return $client;
    }

    public function create(Pipeline $pipeline, array $data, UploadedFile $document) : Client
    {
        $client = $data['isCorporate'] ? $this->createEnterprise($pipeline, $data['enterprise'], $data['person'], $document) : $this->createPerson($data['person'], $document);

        return $this->bind($pipeline, $client, 'contractor');
    }

    public function createPerson(array $personData, UploadedFile $identityDocument): Clientable | null
    {
        if (! $identityDocument) {
            Log::error("Identity document is required for person creation.");

            throw new \Exception("Identity document is required for person creation.");
        }

        DB::connection('tenant')->beginTransaction();

        try {
            $person = new Person();
            $person->safeForceFill($personData);

            // @todo refactor to file manager
            $personData['documents']['uri'] = $identityDocument->store('documents', 'private');
            $person->documents = $personData['documents'];

            $person->phone = $personData['phonePrefix'] . $personData['phone'];
            
            $person->save();

            foreach ($personData['addresses'] as $current) {
                $address = new Address();
                $address->safeForceFill($current);
                $address->person()->associate($person);
                $address->save();
            }
        } catch(\Throwable $e) {
            DB::connection('tenant')->rollBack();

            // Unlink file
            if (isset($personData['documents']['uri'])) {
                try {
                    Storage::disk('private')->delete($personData['documents']['uri']);
                } catch (\Exception $deleteException) {
                    Log::error("Failed to delete identity document: " . $deleteException->getMessage());
                }
            }

            Log::error("Error creating person: " . $e->getMessage());

            throw $e;
        }

        

        DB::connection('tenant')->commit();

        return $person;
    }

    public function createEnterprise(Pipeline $pipeline, array $enterpriseData, array $repData, UploadedFile $document): Clientable
    {
        DB::connection('tenant')->beginTransaction();

        if (empty($repData['id'])) {
            // FIXME: nested transaction 
            $person = $this->createPerson($repData, $document);
        } elseif (! $person = Person::find($repData['id']))  {
            throw new \Exception("Enterprise representative not found.");
        }

        $enterprise = new Enterprise();
        $enterprise->safeForceFill($enterpriseData);
        $enterprise->rep_id = $person->id;
        $enterprise->save();

        DB::connection('tenant')->commit();

        return $enterprise;
    }
}