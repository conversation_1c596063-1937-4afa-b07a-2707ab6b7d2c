<?php namespace Upnovation\Easyprofile\Tasks\Survey;

use App\Models\Coverage;
use App\Models\Form;
use App\Models\Task;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\Tasks\AbstractTask;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class SurveyManager extends AbstractTask
{
    public function initialize(Task $task, ?Task $previousTask = null): Task
    {
        $task = parent::initialize($task, $previousTask);

        // If a profile has already been set, unset the closed timestamp.
        // New pipelines won't have a profile set at this point.
        if ($profile = $task->pipeline->getProfile()) {
            $profile->closed_at = null;
            $profile->save();
        }

        return $task;
    }

    public function finalize(Task $task): Task
    {
        $task = parent::finalize($task);
        
        if (! $form = Form::wherePipelineId($task->pipeline->id)->first()) {
            Log::error("Form not found for pipeline {$task->pipeline->id}");

            abort(500);
        }

        //dump($form->cachedStructure);
        //dump($form->cachedValues);
        //dd($this->stripForm($form->cachedStructure, $form->cachedValues));

        DB::connection('tenant')->beginTransaction();

        $form->cachedResult = $this->stripForm($form->cachedStructure, $form->cachedValues);
        $form->save();

        $profile = $task->pipeline->getProfile();
        $profile->closed_at = new DateTime();
        $profile->save();

        DB::connection('tenant')->commit();

        return $task;
    }

    public function stripForm($fd, $fv)
    {
        $searchValues = array_keys($fv);

        $result = ['sections' => []];

        foreach($fd['sections'] as $section) {
            if (! isset($section['items'])) {
                continue;
            }

            $result['sections'][$section['id']]['id'] = $section['id'];
                $result['sections'][$section['id']]['title'] = $section['title'];
                $result['sections'][$section['id']]['qa'] = null;

            foreach ($section['items'] as $item) {
                if (! isset($item['name'])) {
                    continue;
                }

                

                if (in_array($item['name'], $searchValues)) {
                    
                    $qa = ['q' => $this->getQuestion($item)];

                    if (isset($item['type']) && $item['type'] == 'coverage') {

                        // Deal with pipe-separated coverages.
                        foreach ($fv[$item['name']] as $value) {
                            // If string contains a pipe, then explode and merge the values to $fv[$item['name']] (avoid duplicates).
                            if (strpos($value, '|') !== false) {
                                $fv[$item['name']] = array_merge($fv[$item['name']], explode('|', $value));
                            }
                        }

                        $coverages = Coverage
                            ::whereIn('label', $fv[$item['name']])
                            ->get()
                            ->implode('name', ', ');

                        $qa['a'] = $coverages ?:  __('easyprofile.none');

                    } else {
                        $qa['a'] = $this->getText($item, $fv[$item['name']]);
                    }

                    $result['sections'][$section['id']]['qa'][] = $qa;
                    
                }
            }
        }

        //dd($result);
        return $result;
    }

    protected function getQuestion($item)
    {
        $question = $item['question'] ?? null;

        return $question ?: $item['title'];
    }

    protected function getTranslation($key)
    {
        $text = __($key);

        if ($text && $text != $key) {
            return $text;
        }

        return null;
    }

    protected function getText($item, $value)
    {
        $key = $item['name'];

        if (is_null($value)) {
            return __('easyprofile.no');
        }
        
        if ((isset($item['type']) && $item['type'] == 'boolean') || is_bool($value)) {
            return $value ? __('easyprofile.yes') : __('easyprofile.no');
        }

        if (is_array($value)) {
            // checkbox group
            return "TODO ARRAY";
        }

        // @todo customJob

        try {
            if ($text = $this->getTranslation("easyprofile.survey.{$key}.{$value}")) {
                return $text;
            }

            return $value;
        } catch (\Exception $ex) {
            dump($ex->getMessage());
            dump(func_get_args());
        }
    }
}