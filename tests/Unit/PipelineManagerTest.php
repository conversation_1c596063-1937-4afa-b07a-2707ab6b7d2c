<?php

namespace Tests\Unit;

use App\Models\Task;
use Exception;
use Tests\TestCase;
use Upnovation\Easyprofile\PipelineManager;

class PipelineManagerTest extends TestCase
{
    /** @var PipelineManager */
    protected $manager;

    public function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(PipelineManager::class);
    }

    /** @dataProvider navigationProvider */
    public function test_get_accessible($expectation, $task, $pipelineState)
    {
        if ($expectation === 'exception') {
            $this->expectException(Exception::class);
        }

        $this->assertEquals($expectation, $this->manager->getAccessible($task, $pipelineState));
    }

    public function navigationProvider()
    {
        return [
            // Navigation not set
            /*['exception', new Task(), ''],
            ['exception', new Task(['navigation' => '']), ''],
            
            // Navigation invalid
            ['exception', new Task(['navigation' => 'invalid', 'dependson' => null]), ''],*/

            // Valid navigation values: pipeline.open
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => 'y']), 'open'],
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => null]), 'closed'],
            [true, new Task(['navigation' => 'pipeline.open', 'dependson' => null]), 'open'],

            // Valid navigation values: pipeline.closed
            [false, new Task(['navigation' => 'pipeline.closed', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'pipeline.closed', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'pipeline.closed', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'pipeline.closed', 'dependson' => 'y']), 'open'],
            [true, new Task(['navigation' => 'pipeline.closed', 'dependson' => null]), 'closed'],

            // Valid navigation values: always
            [true, new Task(['navigation' => 'always', 'dependson' => null]), ''],
            [true, new Task(['navigation' => 'always', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'always', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'always', 'dependson' => 'y']), 'open'],
            [true, new Task(['navigation' => 'always', 'dependson' => null]), 'closed'],
            [true, new Task(['navigation' => 'always', 'dependson' => null]), 'x'],

            // Valid navigation values: never
            [false, new Task(['navigation' => 'never', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'never', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'never', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'never', 'dependson' => 'y']), 'open'],
            [false, new Task(['navigation' => 'never', 'dependson' => null]), 'closed'],
            [false, new Task(['navigation' => 'never', 'dependson' => null]), 'x'],

            // Composite states
            [true, new Task(['navigation' => 'always|pipeline.open|pipeline.closed', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'never|pipeline.open|pipeline.closed', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => 'y']), 'open'],
            [true, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => null]), 'closed'],
            [true, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => null]), 'open'],
            [true, new Task(['navigation' => '|pipeline.closed', 'dependson' => null]), 'closed'],
            [true, new Task(['navigation' => 'pipeline.open|', 'dependson' => null]), 'open'],
            [true, new Task(['navigation' => 'pipeline.open|x', 'dependson' => null]), 'open'],
        ];
    }
}
