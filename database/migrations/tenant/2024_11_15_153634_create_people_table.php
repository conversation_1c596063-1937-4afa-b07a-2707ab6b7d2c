<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('people', function (Blueprint $table) {
            $table->id();

            // Anag
            $table->string('name');
            $table->string('lastname');
            $table->string('taxCode')->unique();
            $table->date('birthdate');
            $table->string('birthplace');
            $table->enum('sex', ['M', 'F', 'A']);

            // Contact
            $table->string('email');
            $table->string('pec')->nullable();
            $table->string('phone');

            // Financial
            $table->string('iban')->nullable();
            $table->string('sdi')->nullable();

            // Privacy
            $table->boolean('privacy')->default(false);
            
            // Identity documents
            $table->json('documents');

            $table->dateTime('privacy_accepted_at')->nullable()->default(null);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('people');
    }
};
