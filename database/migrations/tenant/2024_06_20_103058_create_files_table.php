<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('files', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->uuid('uuid')->unique();
            $table->bigInteger('document_id')->unsigned()->nullable()->default(null);
            $table->bigInteger('task_id')->unsigned()->nullable()->default(null);
            $table->string('signatureProviderId')->nullable()->default(null);

            // Signed files are not associated with a document so they need a name.
            $table->string('displayName')->nullable()->default(null);
            $table->string('configName')->unique()->nullable()->default(null);
            $table->enum('type', ['template', 'compiled', 'signable', 'folder']); // rename folder to signed?
            $table->boolean('signed')->default(false);
            $table->string('disk');
            $table->string('path')->nullable()->default(null);
            $table->string('filename');
            $table->timestamps();

            $table->foreign('document_id')->references('id')->on('documents')->onDelete('set null');
            $table->foreign('task_id')->references('id')->on('tasks')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('files');
    }
};
