<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Coverage;
use App\Models\CoverageCategory;
use App\Models\CoverageOption;
use App\Models\CoverageProduct;
use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;

class CatalogSeeder extends Seeder
{
    protected DocumentsInstaller $installer;

    public function __construct(DocumentsInstaller $installer)
    {
        $this->installer = $installer;
    }

    public function setupProduct($id)
    {
        $productData = Catalog::products($id);

        $product = new Product();
        $product->forceFill($productData);
        $product->save();

        // Install product form.
        if ($file = $this->installer->install($product->code)) {
            $product->documents()->attach($file->document);
        }

        // Install product policy template.
        if ($doc = $this->installer->installPolicy($product->code)) {
            $product->documents()->attach($doc);
        }


        CoverageProduct::insert(Catalog::productCoverages($product->id));
        CoverageOption::insert(Catalog::productOptions($product->id));
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Company::factory()->createMany(Catalog::companies());

        CoverageCategory::factory()->createMany(Catalog::categories());

        Coverage::factory()->createMany(Catalog::coverages());

        foreach (Catalog::products() as $product) {
            $this->setupProduct($product['id']);
        }
    }
}
