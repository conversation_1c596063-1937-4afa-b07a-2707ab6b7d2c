<?php namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = new User();

        // load network node POS1
        $node = \App\Models\NetworkNode::whereCode('POS1')->first();

        $user->forceFill([
            'password' => Hash::make("test"),
            'node_id' => $node ? $node->id : null,
            'name' => fake()->name(), 
            'lastname' => fake()->lastName(), 
            'email' => "<EMAIL>", 
            'active' => 1, 
        ])->save();
        $user->addRole('salesman');

        $user = new User();

        $user->forceFill([
            'password' => Hash::make(env('APP_KEY')),
            'name' => fake()->name(), 
            'lastname' => fake()->lastName(), 
            'email' => "<EMAIL>", 
            'active' => 1, 
        ])->save();
        $user->addRole('manager');

        $user->save();
    }
}
