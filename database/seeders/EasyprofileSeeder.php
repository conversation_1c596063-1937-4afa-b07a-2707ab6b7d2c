<?php

namespace Database\Seeders;

use App\Models\Role;
use Dom\Document;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Upnovation\Easyprofile\EasyProfile;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;

class EasyprofileSeeder extends Seeder
{
    protected DocumentsInstaller $installer;

    public function __construct(DocumentsInstaller $installer)
    {
        $this->installer = $installer;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        (new Role)->forceFill(['name' => 'manager'])->save();

        (new Role)->forceFill(['name' => 'salesman'])->save();

        if (EasyProfile::isModuleEnabled(app('currentTenant')->name, 'documents')) {

            // @todo: tenant-aware
            foreach (config('easyprofile.modules.documents.docTypes') as $document) {
                $this->installer->install($document);
            }
        }
    }
}
